---
type: "agent_requested"
description: "Example description"
---
# OYAH! MVP - Agent Build Rules (MB_rules.md)
# Instructions for IDE-integrated AI Agents

## 1. Environment & Dependencies
- node: v18.16.0 or higher
- react-native: v0.7x
- typescript: v5.x
- All dependencies specified in package.json MUST be used. Do not introduce new libraries without updating the project plan.

## 2. File & Directory Structure
Adhere to a strict, feature-sliced directory structure. All new code must be placed appropriately.

/src
|-- /components # Reusable UI components (Button, Card, etc.)
|-- /screens # Top-level screen components (SplashScreen, DashboardScreen, etc.)
|-- /services # Business logic and external communication
| |-- walletService.ts # All Polkadot.js API interactions
| |-- apiService.ts # All backend API calls using axios
| |-- mlService.ts # All TensorFlow Lite model interactions
|-- /state # Zustand store definitions and hooks
|-- /navigation # React Navigation configuration
|-- /types # All TypeScript type definitions (e.g., payload.ts)
|-- /assets # Static assets
|-- /ml # TFLite model files (ocr.tflite, stt.tflite)


## 3. Coding Standards & Conventions
- **Language:** All files must be `.ts` or `.tsx`.
- **Formatting:** Adhere strictly to the Prettier configuration in the project root.
- **Linting:** All code must pass the ESLint rules defined in `.eslintrc.js` without errors.
- **Component Naming:** PascalCase (e.g., `ConfirmationScreen.tsx`).
- **Hook Naming:** camelCase with `use` prefix (e.g., `useWalletStore.ts`).
- **Type Safety:** Avoid using the `any` type. Define explicit types in `/src/types` for all data structures.

## 4. Agent Instructions & Model Context Protocols (MCPs)

### MCP: WalletService
- **Trigger:** When implementing features related to wallet connection or signing.
- **Rule:** All interactions with the `@polkadot/api` or `@polkadot/extension-dapp` libraries MUST be encapsulated within `/src/services/walletService.ts`. No other file should directly import from these libraries. The service must expose simple async functions like `connectWallet()` and `getWalletAddress()`.

### MCP: MLService
- **Trigger:** When implementing on-device OCR or Speech-to-Text.
- **Rule:** All TensorFlow Lite model loading and inference logic MUST be encapsulated within `/src/services/mlService.ts`. This service will take a file path (for an image) or a buffer (for audio) as input and return the processed data. The TFLite models are located in `/src/assets/ml`.

### MCP: ApiService
- **Trigger:** When sending data to or fetching data from the backend.
- **Rule:** All HTTP requests to the backend MUST be made through the `apiService.ts` file. Use an `axios` instance configured with the base URL of the backend. Create typed functions for each endpoint (e.g., `submitResult(payload: ResultPayload)`).

### MCP: StateManagement
- **Trigger:** When accessing or modifying global application state.
- **Rule:** All global state (e.g., user's wallet address, connection status) MUST be managed via the Zustand store defined in `/src/state/walletStore.ts`. Components should use the provided hooks to access state, not manage it locally with `useState` for global data.

## 5. Testing Protocols
- For every UI component created in `/src/components` or `/src/screens`, a corresponding test file (`*.test.tsx`) MUST be generated using Jest and React Native Testing Library.
- All services in `/src/services` MUST have unit tests with mocks for external dependencies (e.g., mock the `axios` or `@polkadot/api` calls).
- Test coverage should be a key metric for completion.