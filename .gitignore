 `1e34532156579 1q456789p0-[]
  `1qtr5yu7[p]
  \|zAH! MVP - Comprehensive .gitignore This file excludes files and directories that should not be tracked in version control

# ===========================
# Node.js Dependencies
# ===========================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ===========================
# Build Outputs
# ===========================
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist/

# ===========================
# Environment Files
# ===========================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# ===========================
# IDE and Editor Files
# ===========================
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# ===========================
# OS Generated Files
# ===========================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# ===========================
# React Native Specific
# ===========================
# Metro bundler cache
.metro-health-check*

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/
ios/*.xcodeproj/project.xcworkspace/xcuserdata/
ios/DerivedData/

# Android
android/build/
android/app/build/
android/.gradle/
android/gradle/
android/gradlew
android/gradlew.bat
android/local.properties
*.keystore
!debug.keystore

# Expo
.expo/
.expo-shared/
expo-env.d.ts

# React Native CLI
.react-native/

# ===========================
# Backend Specific
# ===========================
# TypeScript build output
backend/dist/
backend/build/

# Coverage reports
coverage/
*.lcov
.nyc_output/

# ===========================
# Log Files
# ===========================
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# ===========================
# Temporary Files
# ===========================
*.tmp
*.temp
.tmp/
.temp/
*.cache
.cache/

# ===========================
# Package Manager Files
# ===========================
package-lock.json
yarn.lock
pnpm-lock.yaml
.yarn/
.pnp.*

# ===========================
# Testing
# ===========================
.jest/
jest_*
__tests__/coverage/

# ===========================
# Miscellaneous
# ===========================
# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Database files
*.db
*.sqlite
*.sqlite3

# Certificate files
*.pem
*.key
*.crt
*.p12

# Documentation build
docs/build/
docs/.docusaurus/

# Storybook build outputs
storybook-static/

# ===========================
# Mobile Development
# ===========================
# Flipper
.flipper/

# Bundle artifacts
*.jsbundle

# CocoaPods
ios/Podfile.lock

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/
fastlane/test_output/

# ===========================
# Security
# ===========================
# Private keys
*.private
*.secret

# API keys
.api-keys
secrets.json

# ===========================
# Development Tools
# ===========================
# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# TypeScript cache
*.tsbuildinfo

# Webpack cache
.webpack/

# Parcel cache
.parcel-cache/

# ===========================
# Cloud & Deployment
# ===========================
# Vercel
.vercel/

# Netlify
.netlify/

# Firebase
.firebase/
firebase-debug.log*
firestore-debug.log*

# Heroku
.heroku/

# ===========================
# Custom Project Files
# ===========================
# Demo files (keep demo.html for demonstration)
# demo.html

# Test API responses
test-responses/

# Local configuration
config.local.js
config.local.json

# Backup directories
backup/
backups/
