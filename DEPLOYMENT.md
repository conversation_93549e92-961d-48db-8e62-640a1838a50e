# OYAH! MVP Deployment Guide

This guide provides step-by-step instructions for deploying the OYAH! MVP to production environments.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Backend API   │    │   Database      │
│  (React Native) │◄──►│   (Node.js)     │◄──►│  (In-Memory)    │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Backend Deployment

### Option 1: Heroku Deployment

1. **Prepare for deployment**:
```bash
cd backend
npm run build
```

2. **Create Heroku app**:
```bash
heroku create oyah-backend
```

3. **Set environment variables**:
```bash
heroku config:set NODE_ENV=production
heroku config:set PORT=3000
heroku config:set ALLOWED_ORIGINS=https://your-domain.com
```

4. **Deploy**:
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

### Option 2: DigitalOcean Droplet

1. **Create droplet** (Ubuntu 20.04 LTS)

2. **Setup server**:
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
sudo npm install -g pm2
```

3. **Deploy application**:
```bash
# Clone repository
git clone https://github.com/your-repo/oyah.git
cd oyah/backend

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Start with PM2
pm2 start dist/index.js --name "oyah-backend"
pm2 startup
pm2 save
```

4. **Setup Nginx reverse proxy**:
```bash
sudo apt install nginx

# Create Nginx configuration
sudo nano /etc/nginx/sites-available/oyah
```

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/oyah /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Option 3: AWS EC2

1. **Launch EC2 instance** (t3.micro for MVP)

2. **Configure security groups**:
   - HTTP (80)
   - HTTPS (443)
   - SSH (22)
   - Custom TCP (3000) for development

3. **Follow DigitalOcean steps** for server setup

### Docker Deployment

1. **Create Dockerfile**:
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY dist ./dist

EXPOSE 3000

USER node

CMD ["npm", "start"]
```

2. **Build and run**:
```bash
docker build -t oyah-backend .
docker run -p 3000:3000 -e NODE_ENV=production oyah-backend
```

## 📱 Mobile App Deployment

### Android Deployment

1. **Generate signing key**:
```bash
cd mobile/android/app
keytool -genkeypair -v -storetype PKCS12 -keystore oyah-release-key.keystore -alias oyah-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

2. **Configure signing** in `android/gradle.properties`:
```properties
OYAH_UPLOAD_STORE_FILE=oyah-release-key.keystore
OYAH_UPLOAD_KEY_ALIAS=oyah-key-alias
OYAH_UPLOAD_STORE_PASSWORD=your_password
OYAH_UPLOAD_KEY_PASSWORD=your_password
```

3. **Build release APK**:
```bash
cd mobile
npm run build:android:release
```

4. **Upload to Google Play Console**

### iOS Deployment

1. **Configure signing** in Xcode:
   - Open `mobile/ios/OyahMobile.xcworkspace`
   - Select project → Signing & Capabilities
   - Configure Team and Bundle Identifier

2. **Build for release**:
   - Product → Archive
   - Distribute App → App Store Connect

3. **Upload to App Store Connect**

### Development Distribution

#### Android APK Distribution
```bash
cd mobile
npx react-native build-android --mode=release
```

#### iOS TestFlight
- Use Xcode to create archive
- Upload to App Store Connect
- Add to TestFlight for beta testing

## 🔧 Environment Configuration

### Backend Environment Variables

**Production (.env)**:
```bash
NODE_ENV=production
PORT=3000
ALLOWED_ORIGINS=https://your-domain.com,https://app.your-domain.com
MIN_SUBMISSIONS=3
MIN_UNIQUE_WALLETS=3
MAJORITY_THRESHOLD=0.6
LOG_LEVEL=info
```

**Development (.env.development)**:
```bash
NODE_ENV=development
PORT=3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081
MIN_SUBMISSIONS=2
MIN_UNIQUE_WALLETS=2
MAJORITY_THRESHOLD=0.5
LOG_LEVEL=debug
```

### Mobile App Configuration

**Production API URL**:
```typescript
// mobile/src/services/apiService.ts
const API_BASE_URL = 'https://api.your-domain.com/api/v1';
```

**Development API URL**:
```typescript
const API_BASE_URL = 'http://localhost:3000/api/v1';
```

## 🔒 Security Considerations

### Backend Security
- Enable HTTPS with SSL certificates
- Configure CORS properly
- Implement rate limiting
- Add request validation
- Set up monitoring and logging

### Mobile Security
- Obfuscate code for production
- Implement certificate pinning
- Validate all user inputs
- Secure storage for sensitive data

## 📊 Monitoring & Analytics

### Backend Monitoring
```bash
# Install monitoring tools
npm install --save express-rate-limit helmet compression

# Add to server
const rateLimit = require('express-rate-limit');
app.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
}));
```

### Application Monitoring
- **Heroku**: Built-in metrics
- **AWS**: CloudWatch
- **DigitalOcean**: Monitoring add-on
- **Custom**: PM2 monitoring

### Error Tracking
- **Sentry**: For error tracking
- **LogRocket**: For session replay
- **Crashlytics**: For mobile crash reporting

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy Backend

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - run: npm test
      - name: Deploy to Heroku
        uses: akhileshns/heroku-deploy@v3.12.12
        with:
          heroku_api_key: ${{secrets.HEROKU_API_KEY}}
          heroku_app_name: "oyah-backend"
          heroku_email: "<EMAIL>"
```

## 🧪 Production Testing

### Health Checks
```bash
# Backend health
curl https://api.your-domain.com/health

# API functionality
curl https://api.your-domain.com/api/v1/getTally
```

### Load Testing
```bash
# Install artillery
npm install -g artillery

# Create test script
artillery quick --count 10 --num 100 https://api.your-domain.com/api/v1/getTally
```

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] All tests passing
- [ ] Environment variables configured
- [ ] SSL certificates ready
- [ ] Domain names configured
- [ ] Database backups (if applicable)

### Backend Deployment
- [ ] Build successful
- [ ] Environment variables set
- [ ] Health check passing
- [ ] CORS configured
- [ ] Monitoring enabled

### Mobile Deployment
- [ ] Release build successful
- [ ] API endpoints updated
- [ ] App store metadata ready
- [ ] Screenshots prepared
- [ ] Privacy policy updated

### Post-Deployment
- [ ] Health checks passing
- [ ] Monitoring alerts configured
- [ ] Performance metrics baseline
- [ ] User acceptance testing
- [ ] Documentation updated

## 🆘 Rollback Procedures

### Backend Rollback
```bash
# Heroku
heroku rollback v123

# PM2
pm2 stop oyah-backend
git checkout previous-version
npm run build
pm2 start dist/index.js
```

### Mobile Rollback
- **Android**: Upload previous APK version
- **iOS**: Reject current version, promote previous

## 📞 Support & Maintenance

### Monitoring Alerts
- Server downtime
- High error rates
- Performance degradation
- Security incidents

### Regular Maintenance
- Security updates
- Dependency updates
- Performance optimization
- Database cleanup (when applicable)

---

**Successful deployment ensures OYAH! MVP is accessible and reliable for election witnessing.**
