# OYAH! MVP - Decentralized Election Witness dApp

**OYAH!** is a decentralized mobile application (dApp) designed to bring radical transparency and trust to the electoral process. It empowers ordinary citizens and party agents to act as "witnesses" by securely submitting polling station results using Web3 technology and crowdsourced consensus.

## 🎯 Project Overview

The MVP demonstrates the core user flow: **Connect → Capture → Confirm → Transmit → Tally**

### Key Features

- **Web3 Wallet Integration**: Connect with Nova Wallet using Polkadot.js
- **On-Device Processing**: OCR and Speech-to-Text using TensorFlow Lite
- **Crowdsourced Consensus**: Decentralized verification of election results
- **Real-time Dashboard**: Live election tally with polling station status
- **Secure Transmission**: Blockchain-based identity and data integrity

## 🏗️ Architecture

### Mobile dApp (React Native + TypeScript)
- **Frontend**: React Native with TypeScript
- **State Management**: Zustand
- **Styling**: Styled Components
- **Web3**: Polkadot.js API
- **ML**: TensorFlow Lite (OCR & STT)
- **Navigation**: React Navigation

### Backend (Node.js + TypeScript)
- **Server**: Express.js with TypeScript
- **Consensus Engine**: Custom algorithm for result verification
- **Storage**: In-memory (MVP) / Redis (Production)
- **API**: RESTful endpoints for data submission and retrieval

## 📁 Project Structure

```
OYAH/
├── mobile/                 # React Native dApp
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── screens/        # Screen components
│   │   ├── services/       # Business logic services
│   │   ├── state/          # Zustand stores
│   │   ├── navigation/     # Navigation configuration
│   │   ├── types/          # TypeScript definitions
│   │   ├── assets/         # Static assets
│   │   └── ml/             # TensorFlow Lite models
│   ├── package.json
│   └── tsconfig.json
├── backend/                # Node.js backend
│   ├── src/
│   │   ├── routes/         # API endpoints
│   │   ├── services/       # Business logic
│   │   ├── types/          # TypeScript definitions
│   │   ├── middleware/     # Express middleware
│   │   └── utils/          # Utility functions
│   ├── package.json
│   └── tsconfig.json
└── README.md
```

## 🚀 Quick Start

### Prerequisites

- Node.js v18.16.0 or higher
- React Native development environment
- Nova Wallet (for testing)

### Backend Setup

1. Navigate to backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Start development server:
```bash
npm run dev
```

The backend will be available at `http://localhost:3000`

### Mobile App Setup

1. Navigate to mobile directory:
```bash
cd mobile
```

2. Install dependencies:
```bash
npm install
```

3. Start Metro bundler:
```bash
npm start
```

4. Run on device/simulator:
```bash
# For Android
npm run android

# For iOS
npm run ios
```

## 📱 User Flow

1. **Connect Wallet**: User connects Nova Wallet to authenticate
2. **Choose Method**: Select image capture (OCR) or audio recording (STT)
3. **Capture Data**: Take photo of Form 34A or record announcement
4. **Confirm Results**: Review and edit extracted election results
5. **Submit**: Transmit verified data to consensus engine
6. **View Dashboard**: Monitor real-time election tally

## 🔧 API Endpoints

### POST /api/v1/submitResult
Submit election results for consensus processing.

**Request Body:**
```json
{
  "walletAddress": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
  "pollingStationId": "KAS-01-123",
  "gpsCoordinates": {"latitude": -1.286389, "longitude": 36.817223},
  "timestamp": "2025-08-09T14:30:00Z",
  "results": {"candidateA": 152, "candidateB": 210, "spoilt": 12},
  "submissionType": "image_ocr"
}
```

### GET /api/v1/getTally
Retrieve current election tally and polling station status.

**Response:**
```json
{
  "nationalTally": {"candidateA": 125430, "candidateB": 145210, "spoilt": 8950},
  "pollingStations": [
    {"id": "KAS-01-123", "status": "Verified", "results": {...}},
    {"id": "KAS-01-124", "status": "Pending Consensus", "results": null}
  ],
  "totalStations": 150,
  "verifiedStations": 89,
  "lastUpdated": "2025-08-09T15:30:00Z"
}
```

## 🔒 Consensus Algorithm

The MVP implements a simple consensus mechanism:

1. **Grouping**: Results are grouped by identical vote counts
2. **Verification**: Requires minimum 3 unique wallet addresses
3. **Majority**: Group must represent 60%+ of submissions
4. **Status**: Station marked as "Verified" when consensus achieved

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm test
```

### Mobile Tests
```bash
cd mobile
npm test
```

## 🚀 Deployment

### Backend Deployment
The backend can be deployed to any Node.js hosting service:
- Heroku
- Vercel
- AWS EC2
- DigitalOcean

### Mobile App Distribution
- Android: Google Play Store or APK distribution
- iOS: Apple App Store or TestFlight

## 🛠️ Development Notes

### MVP Limitations
- **Mock ML Models**: OCR and STT use simulated processing
- **In-Memory Storage**: Data doesn't persist between restarts
- **Basic Consensus**: Simple majority-based verification
- **Limited Error Handling**: Basic error scenarios covered

### Production Considerations
- Implement actual TensorFlow Lite models
- Add persistent database (PostgreSQL/MongoDB)
- Enhanced security and validation
- Comprehensive error handling
- Performance optimization
- Automated testing suite

## 📄 License

This project is part of the OYAH! MVP development and is intended for demonstration purposes.

## 🤝 Contributing

This is an MVP implementation. For production development, please follow the established coding standards and testing protocols outlined in the project documentation.

---

**Built with ❤️ for transparent elections**
