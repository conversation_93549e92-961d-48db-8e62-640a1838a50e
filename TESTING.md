# OYAH! MVP Testing Guide

This document provides comprehensive testing instructions for the OYAH! MVP implementation.

## 🧪 Testing Overview

The OYAH! MVP includes both automated tests and manual testing procedures to ensure the complete user flow works correctly.

### Test Coverage
- ✅ Backend API endpoints
- ✅ Consensus algorithm
- ✅ Data validation
- ✅ Error handling
- ✅ Mobile app navigation
- ✅ State management
- ✅ End-to-end user flow

## 🔧 Backend Testing

### Automated API Testing

1. **Start the backend server**:
```bash
cd backend
npm run dev
```

2. **Run the API test suite**:
```bash
node test-api.js
```

The test script will verify:
- Health check endpoint
- Result submission with valid data
- Consensus algorithm functionality
- Duplicate submission prevention
- Invalid data validation
- Tally retrieval

### Expected Test Results
```
🚀 Starting OYAH! Backend API Tests

🔍 Testing Health Check...
✅ Health check passed

📤 Testing Submit Result 1...
✅ Submission successful

📤 Testing Submit Result 2...
✅ Submission successful

📤 Testing Submit Result 3...
✅ Submission successful (Consensus achieved!)

📊 Testing Get Tally...
✅ Tally retrieved successfully

🔄 Testing Duplicate Submission Prevention...
✅ Duplicate submission correctly rejected

❌ Testing Invalid Data Validation...
✅ Invalid data correctly rejected

📋 Test Summary: ✅ Passed: 7/7
🎉 All tests passed! Backend is working correctly.
```

### Manual Backend Testing

#### Test Consensus Algorithm
1. Submit 3+ identical results from different wallets
2. Verify station status changes to "Verified"
3. Check national tally updates correctly

#### Test API Endpoints
```bash
# Health check
curl http://localhost:3000/health

# Submit result
curl -X POST http://localhost:3000/api/v1/submitResult \
  -H "Content-Type: application/json" \
  -d '{
    "walletAddress": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
    "pollingStationId": "TEST-001",
    "gpsCoordinates": {"latitude": -1.286389, "longitude": 36.817223},
    "timestamp": "2025-01-30T10:00:00Z",
    "results": {"candidateA": 100, "candidateB": 150, "spoilt": 5},
    "submissionType": "image_ocr"
  }'

# Get tally
curl http://localhost:3000/api/v1/getTally
```

## 📱 Mobile App Testing

### Prerequisites
1. Backend server running on `http://localhost:3000`
2. Mobile app running on device/simulator
3. Nova Wallet installed (for wallet testing)

### Manual Testing Checklist

#### 1. Wallet Connection Flow
- [ ] App starts with Splash Screen
- [ ] "Connect Wallet to Witness" button is visible
- [ ] Tapping button initiates wallet connection
- [ ] Mock wallet connection succeeds
- [ ] Navigation to Main Action Screen occurs
- [ ] Wallet address is displayed correctly

#### 2. Main Action Screen
- [ ] Welcome message shows wallet address
- [ ] Two action buttons are visible:
  - [ ] "Capture Form Image" with camera icon
  - [ ] "Record Official Announcement" with microphone icon
- [ ] "View Live Tally Dashboard" button is visible
- [ ] Navigation to respective screens works

#### 3. Image Capture Flow
- [ ] Camera placeholder screen appears
- [ ] "Capture Form 34A" button works
- [ ] Mock image capture succeeds
- [ ] "Retake" and "Process with OCR" options appear
- [ ] OCR processing shows loading state
- [ ] Navigation to Confirmation Screen with extracted data

#### 4. Audio Capture Flow
- [ ] Audio recording interface appears
- [ ] "Start Recording" button works
- [ ] Recording timer displays correctly
- [ ] "Stop Recording" button works
- [ ] "Record Again" and "Process with STT" options appear
- [ ] STT processing shows loading state
- [ ] Navigation to Confirmation Screen with extracted data

#### 5. Confirmation Screen
- [ ] Extracted results display correctly
- [ ] Data source indicator shows (OCR/STT)
- [ ] Polling station ID field is editable
- [ ] Vote count fields are editable
- [ ] Total votes calculation is correct
- [ ] Warning message is displayed
- [ ] "Confirm & Submit" button works
- [ ] Submission success dialog appears
- [ ] Navigation options to Dashboard/Main Action

#### 6. Dashboard Screen
- [ ] National tally displays correctly
- [ ] Statistics cards show proper values
- [ ] Polling stations list appears
- [ ] Station status indicators work (✅/⏳)
- [ ] Pull-to-refresh functionality works
- [ ] Auto-refresh every 30 seconds
- [ ] Last updated timestamp shows
- [ ] Back navigation works

### Error Handling Tests

#### Network Errors
- [ ] Disconnect from internet
- [ ] Try submitting results
- [ ] Verify error message appears
- [ ] Reconnect and retry successfully

#### Invalid Data
- [ ] Enter negative vote counts
- [ ] Leave polling station ID empty
- [ ] Verify validation messages

#### Backend Unavailable
- [ ] Stop backend server
- [ ] Try accessing dashboard
- [ ] Verify appropriate error handling

## 🔄 End-to-End Testing

### Complete User Journey Test

1. **Setup**:
   - Start backend server
   - Launch mobile app
   - Ensure clean state

2. **User Flow**:
   ```
   Splash Screen → Connect Wallet → Main Action Screen
   ↓
   Choose Image Capture → Capture Form → Process OCR
   ↓
   Confirmation Screen → Edit Results → Submit
   ↓
   Dashboard → View Results → Verify Consensus
   ```

3. **Verification Points**:
   - [ ] Wallet connection successful
   - [ ] Image capture simulation works
   - [ ] OCR processing completes
   - [ ] Results editable and submittable
   - [ ] Backend receives submission
   - [ ] Dashboard shows updated data
   - [ ] Consensus algorithm processes correctly

### Multi-User Consensus Test

1. **Simulate Multiple Users**:
   - Use different wallet addresses
   - Submit identical results for same station
   - Verify consensus achievement

2. **Test Scenarios**:
   - [ ] 1 submission: Status = "Pending Consensus"
   - [ ] 2 submissions: Status = "Pending Consensus"
   - [ ] 3+ identical submissions: Status = "Verified"
   - [ ] Conflicting submissions: Status = "Pending Consensus"

## 🐛 Common Issues & Solutions

### Backend Issues
- **Port 3000 in use**: `lsof -ti:3000 | xargs kill -9`
- **CORS errors**: Check ALLOWED_ORIGINS in .env
- **TypeScript errors**: Run `npm run type-check`

### Mobile App Issues
- **Metro bundler cache**: `npm start -- --reset-cache`
- **Navigation errors**: Check screen imports
- **State not updating**: Verify Zustand store usage

### Integration Issues
- **API connection**: Verify backend URL in apiService
- **Wallet connection**: Check Polkadot.js configuration
- **Data format**: Ensure payload matches API schema

## 📊 Performance Testing

### Backend Performance
- Monitor response times during load
- Check memory usage with multiple submissions
- Verify consensus algorithm efficiency

### Mobile Performance
- Test on low-end devices
- Monitor memory usage during ML processing
- Check navigation performance

## ✅ Test Completion Checklist

- [ ] All backend API tests pass
- [ ] Complete mobile user flow tested
- [ ] Error handling verified
- [ ] Multi-user consensus tested
- [ ] Performance acceptable
- [ ] Documentation updated
- [ ] Known issues documented

## 📝 Test Reporting

Document any issues found during testing:

1. **Issue Description**: Clear description of the problem
2. **Steps to Reproduce**: Exact steps to recreate the issue
3. **Expected Behavior**: What should happen
4. **Actual Behavior**: What actually happens
5. **Environment**: Device, OS, app version
6. **Severity**: Critical, High, Medium, Low

---

**Testing ensures the OYAH! MVP delivers a reliable and secure election witnessing experience.**
