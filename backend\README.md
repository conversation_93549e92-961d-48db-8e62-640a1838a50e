# OYAH! Backend - Consensus Engine

Node.js backend service for the OYAH! decentralized election witness system, implementing consensus algorithms and API endpoints.

## 🏗️ Architecture

### Technology Stack
- **Node.js**: JavaScript runtime
- **Express.js**: Web application framework
- **TypeScript**: Type-safe JavaScript
- **Joi**: Data validation
- **CORS**: Cross-origin resource sharing
- **Helmet**: Security middleware
- **Morgan**: HTTP request logging

### Directory Structure
```
src/
├── routes/         # API endpoint handlers
│   ├── api.ts              # Main API router
│   ├── submitResult.ts     # Result submission endpoint
│   └── getTally.ts         # Tally retrieval endpoint
├── services/       # Business logic services
│   ├── dataStorage.ts      # In-memory data storage
│   └── consensusService.ts # Consensus algorithm
├── types/          # TypeScript definitions
│   ├── api.ts              # API-related types
│   └── consensus.ts        # Consensus-related types
├── middleware/     # Express middleware
│   ├── requestLogger.ts    # Custom request logging
│   └── errorHandler.ts     # Global error handling
├── utils/          # Utility functions
└── index.ts        # Main server file
```

## 🚀 Setup Instructions

### Prerequisites
- Node.js v18.16.0+
- npm or yarn package manager

### Installation
1. Install dependencies:
```bash
npm install
```

2. Create environment file:
```bash
cp .env.example .env
```

3. Configure environment variables:
```bash
# Edit .env file
PORT=3000
NODE_ENV=development
```

4. Start development server:
```bash
npm run dev
```

5. Start production server:
```bash
npm run build
npm start
```

## 📡 API Endpoints

### Health Check
```
GET /health
GET /api/v1/health
```

### Submit Election Result
```
POST /api/v1/submitResult
```

**Request Body:**
```json
{
  "walletAddress": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
  "pollingStationId": "KAS-01-123",
  "gpsCoordinates": {
    "latitude": -1.286389,
    "longitude": 36.817223
  },
  "timestamp": "2025-08-09T14:30:00Z",
  "results": {
    "candidateA": 152,
    "candidateB": 210,
    "spoilt": 12
  },
  "submissionType": "image_ocr"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Result submitted successfully",
  "data": {
    "submissionId": "uuid-here",
    "pollingStationId": "KAS-01-123",
    "consensusStatus": "Pending Consensus",
    "consensusReason": "Insufficient submissions (1/3)"
  }
}
```

### Get Election Tally
```
GET /api/v1/getTally
```

**Response:**
```json
{
  "success": true,
  "data": {
    "nationalTally": {
      "candidateA": 125430,
      "candidateB": 145210,
      "spoilt": 8950
    },
    "pollingStations": [
      {
        "id": "KAS-01-123",
        "status": "Verified",
        "results": {
          "candidateA": 152,
          "candidateB": 210,
          "spoilt": 12
        },
        "submissionCount": 5,
        "lastUpdated": "2025-08-09T14:30:00Z"
      }
    ],
    "totalStations": 150,
    "verifiedStations": 89,
    "lastUpdated": "2025-08-09T15:30:00Z"
  }
}
```

## 🔧 Consensus Algorithm

### Overview
The consensus engine implements a simple but effective verification mechanism:

1. **Data Clustering**: Group submissions by identical results
2. **Unique Wallet Verification**: Ensure minimum unique contributors
3. **Majority Threshold**: Require significant agreement percentage
4. **Status Updates**: Mark stations as verified when consensus achieved

### Configuration
```typescript
interface ConsensusConfig {
  minimumSubmissions: number;      // Default: 3
  minimumUniqueWallets: number;    // Default: 3
  majorityThreshold: number;       // Default: 0.6 (60%)
}
```

### Process Flow
1. Submission received and validated
2. Check for duplicate wallet submissions
3. Store submission and group by results
4. Evaluate consensus requirements
5. Update station status if verified
6. Return consensus result

## 💾 Data Storage

### MVP Implementation
- **In-Memory Storage**: Fast access, no persistence
- **Data Structures**: Maps and Sets for efficient operations
- **Statistics Tracking**: Real-time metrics calculation

### Production Considerations
- **Redis**: For distributed caching
- **PostgreSQL**: For persistent storage
- **MongoDB**: For document-based storage

## 🔒 Security Features

### Request Validation
- **Joi Schemas**: Comprehensive input validation
- **Type Safety**: TypeScript for compile-time checks
- **Sanitization**: Input cleaning and normalization

### Security Middleware
- **Helmet**: Security headers
- **CORS**: Cross-origin protection
- **Rate Limiting**: Request throttling (production)
- **Input Validation**: Comprehensive data validation

## 🧪 Testing

### Run Tests
```bash
npm test
```

### Test Coverage
```bash
npm run test:coverage
```

### Linting
```bash
npm run lint
```

### Type Checking
```bash
npm run type-check
```

## 📊 Monitoring & Logging

### Request Logging
- **Morgan**: HTTP request logging
- **Custom Logger**: Detailed API operation logs
- **Error Tracking**: Comprehensive error logging

### Health Monitoring
```bash
curl http://localhost:3000/health
```

### Statistics Endpoint
The `/api/v1/getTally` endpoint provides real-time statistics:
- Total polling stations
- Verified stations
- Pending stations
- Total submissions

## 🚀 Deployment

### Environment Variables
```bash
PORT=3000
NODE_ENV=production
ALLOWED_ORIGINS=https://yourdomain.com
MIN_SUBMISSIONS=3
MIN_UNIQUE_WALLETS=3
MAJORITY_THRESHOLD=0.6
```

### Docker Deployment
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["npm", "start"]
```

### Cloud Deployment
- **Heroku**: Simple deployment with git
- **AWS EC2**: Full control over environment
- **DigitalOcean**: Cost-effective hosting
- **Vercel**: Serverless deployment

## 🔄 API Response Format

### Success Response
```json
{
  "success": true,
  "data": {...},
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error type",
  "message": "Detailed error message"
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**:
```bash
lsof -ti:3000 | xargs kill -9
```

2. **TypeScript compilation errors**:
```bash
npm run type-check
```

3. **Module resolution issues**:
```bash
npm run build
```

### Performance Optimization
- **Compression**: Gzip response compression
- **Caching**: In-memory data caching
- **Connection Pooling**: Database connections
- **Load Balancing**: Multiple server instances

## 📚 Additional Resources

- [Express.js Documentation](https://expressjs.com/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Joi Validation Documentation](https://joi.dev/api/)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)
