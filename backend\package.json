{"name": "oyah-backend", "version": "1.0.0", "description": "OYAH! Backend Consensus Engine", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "jest", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"axios": "^1.11.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "morgan": "^1.10.0", "redis": "^4.6.7", "uuid": "^9.0.0"}, "devDependencies": {"@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/morgan": "^1.9.4", "@types/node": "^20.4.5", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.45.0", "jest": "^29.6.1", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.16.0"}}