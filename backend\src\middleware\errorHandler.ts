/**
 * Error Handler Middleware
 * Global error handling for the API
 */

import {Request, Response, NextFunction} from 'express';
import {ApiResponse} from '../types';

export const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.error('🚨 Error:', err);

  // Default error response
  let statusCode = 500;
  let message = 'Internal server error';

  // Handle specific error types
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation failed';
  } else if (err.name === 'UnauthorizedError') {
    statusCode = 401;
    message = 'Unauthorized';
  } else if (err.status) {
    statusCode = err.status;
    message = err.message;
  }

  const response: ApiResponse = {
    success: false,
    error: message,
    message: process.env.NODE_ENV === 'development' ? err.message : message,
  };

  res.status(statusCode).json(response);
};
