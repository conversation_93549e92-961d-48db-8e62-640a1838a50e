/**
 * API Routes for OYAH! Backend
 * Main API endpoints for result submission and tally retrieval
 */

import {Router} from 'express';
import {submitResultHandler} from './submitResult';
import {getTallyHandler} from './getTally';

const router = Router();

// Submit election result endpoint
router.post('/submitResult', submitResultHandler);

// Get election tally endpoint
router.get('/getTally', getTallyHandler);

// Health check endpoint (specific to API)
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'OYAH! API',
    timestamp: new Date().toISOString(),
  });
});

export default router;
