/**
 * Get Tally API Endpoint
 * Handles GET /api/v1/getTally requests
 */

import {Request, Response} from 'express';
import {TallyResponse, PollingStationStatus, ApiResponse} from '../types';
import dataStorage from '../services/dataStorage';

/**
 * Get Tally Handler
 */
export const getTallyHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('Received get tally request');

    // Get national tally from verified stations
    const nationalTally = dataStorage.getNationalTally();

    // Get all polling stations data
    const allStations = dataStorage.getAllPollingStations();
    
    // Convert to response format
    const pollingStations: PollingStationStatus[] = allStations.map(station => ({
      id: station.id,
      status: station.status,
      results: station.verifiedResult,
      submissionCount: station.submissions.length,
      lastUpdated: station.lastUpdated,
    }));

    // Get statistics
    const stats = dataStorage.getStatistics();

    // Prepare response
    const tallyResponse: TallyResponse = {
      nationalTally,
      pollingStations,
      totalStations: stats.totalStations,
      verifiedStations: stats.verifiedStations,
      lastUpdated: new Date().toISOString(),
    };

    const response: ApiResponse<TallyResponse> = {
      success: true,
      data: tallyResponse,
      message: 'Tally retrieved successfully',
    };

    console.log(`Tally response: ${stats.verifiedStations}/${stats.totalStations} stations verified`);
    
    res.status(200).json(response);
  } catch (error) {
    console.error('Error in getTallyHandler:', error);
    
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
      message: 'Failed to retrieve tally',
    };
    
    res.status(500).json(response);
  }
};
