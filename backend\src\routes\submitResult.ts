/**
 * Submit Result API Endpoint
 * Handles POST /api/v1/submitResult requests
 */

import {Request, Response} from 'express';
import Joi from 'joi';
import {v4 as uuidv4} from 'uuid';
import {ResultPayload, Submission, ApiResponse} from '../types';
import dataStorage from '../services/dataStorage';
import consensusService from '../services/consensusService';

// Validation schema for result payload
const resultPayloadSchema = Joi.object({
  walletAddress: Joi.string().required().min(10).max(100),
  pollingStationId: Joi.string().required().min(3).max(50),
  gpsCoordinates: Joi.object({
    latitude: Joi.number().required().min(-90).max(90),
    longitude: Joi.number().required().min(-180).max(180),
  }).required(),
  timestamp: Joi.string().isoDate().required(),
  results: Joi.object({
    candidateA: Joi.number().integer().min(0).required(),
    candidateB: Joi.number().integer().min(0).required(),
    spoilt: Joi.number().integer().min(0).required(),
  }).required(),
  submissionType: Joi.string().valid('image_ocr', 'audio_stt').required(),
});

/**
 * Submit Result Handler
 */
export const submitResultHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('Received submit result request:', req.body);

    // Validate request body
    const {error, value} = resultPayloadSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        message: error.details[0].message,
      };
      res.status(400).json(response);
      return;
    }

    const payload: ResultPayload = value;

    // Check if wallet has already submitted for this station
    const hasSubmitted = dataStorage.hasWalletSubmittedForStation(
      payload.walletAddress,
      payload.pollingStationId,
    );

    if (hasSubmitted) {
      const response: ApiResponse = {
        success: false,
        error: 'Duplicate submission',
        message: 'This wallet has already submitted results for this polling station',
      };
      res.status(409).json(response);
      return;
    }

    // Create submission object
    const submission: Submission = {
      id: uuidv4(),
      walletAddress: payload.walletAddress,
      pollingStationId: payload.pollingStationId,
      results: payload.results,
      submissionType: payload.submissionType,
      timestamp: payload.timestamp,
      gpsCoordinates: payload.gpsCoordinates,
    };

    // Store the submission
    dataStorage.storeSubmission(submission);

    // Process consensus for this polling station
    const consensusResult = consensusService.processConsensus(payload.pollingStationId);
    
    console.log(`Consensus result for station ${payload.pollingStationId}:`, consensusResult.reason);

    // Prepare response
    const response: ApiResponse = {
      success: true,
      message: 'Result submitted successfully',
      data: {
        submissionId: submission.id,
        pollingStationId: payload.pollingStationId,
        consensusStatus: consensusResult.isVerified ? 'Verified' : 'Pending Consensus',
        consensusReason: consensusResult.reason,
      },
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Error in submitResultHandler:', error);
    
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
      message: 'Failed to process submission',
    };
    
    res.status(500).json(response);
  }
};
