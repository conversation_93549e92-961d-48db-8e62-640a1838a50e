/**
 * Consensus Service for OYAH! Backend
 * Implements the MVP consensus algorithm for election result verification
 */

import {
  Submission,
  ConsensusGroup,
  ConsensusResult,
  ConsensusConfig,
  ElectionResults,
} from '../types';
import dataStorage from './dataStorage';

class ConsensusService {
  private config: ConsensusConfig = {
    minimumSubmissions: 3,
    minimumUniqueWallets: 3,
    majorityThreshold: 0.6, // 60% majority required
  };

  /**
   * Process consensus for a polling station
   */
  processConsensus(pollingStationId: string): ConsensusResult {
    const submissions = dataStorage.getSubmissionsForStation(pollingStationId);
    
    if (submissions.length < this.config.minimumSubmissions) {
      return {
        isVerified: false,
        verifiedResult: null,
        consensusGroup: null,
        totalSubmissions: submissions.length,
        reason: `Insufficient submissions (${submissions.length}/${this.config.minimumSubmissions})`,
      };
    }

    // Group submissions by identical results
    const consensusGroups = this.groupSubmissionsByResults(submissions);
    
    // Find the largest consensus group
    const largestGroup = this.findLargestConsensusGroup(consensusGroups);
    
    if (!largestGroup) {
      return {
        isVerified: false,
        verifiedResult: null,
        consensusGroup: null,
        totalSubmissions: submissions.length,
        reason: 'No consensus groups found',
      };
    }

    // Check if the largest group meets consensus requirements
    const consensusResult = this.evaluateConsensus(largestGroup, submissions.length);
    
    if (consensusResult.isVerified) {
      // Update the polling station status
      dataStorage.updatePollingStationStatus(
        pollingStationId,
        'Verified',
        largestGroup.results,
      );
    }

    return {
      ...consensusResult,
      consensusGroup: largestGroup,
      totalSubmissions: submissions.length,
    };
  }

  /**
   * Group submissions by identical results
   */
  private groupSubmissionsByResults(submissions: Submission[]): ConsensusGroup[] {
    const groupsMap = new Map<string, ConsensusGroup>();

    for (const submission of submissions) {
      const resultsKey = this.createResultsKey(submission.results);
      
      let group = groupsMap.get(resultsKey);
      if (!group) {
        group = {
          results: submission.results,
          submissions: [],
          uniqueWallets: new Set(),
          count: 0,
        };
        groupsMap.set(resultsKey, group);
      }

      group.submissions.push(submission);
      group.uniqueWallets.add(submission.walletAddress);
      group.count = group.submissions.length;
    }

    return Array.from(groupsMap.values());
  }

  /**
   * Create a unique key for election results
   */
  private createResultsKey(results: ElectionResults): string {
    return `${results.candidateA}-${results.candidateB}-${results.spoilt}`;
  }

  /**
   * Find the largest consensus group
   */
  private findLargestConsensusGroup(groups: ConsensusGroup[]): ConsensusGroup | null {
    if (groups.length === 0) return null;

    return groups.reduce((largest, current) => {
      // Prioritize by unique wallets first, then by total count
      if (current.uniqueWallets.size > largest.uniqueWallets.size) {
        return current;
      } else if (current.uniqueWallets.size === largest.uniqueWallets.size) {
        return current.count > largest.count ? current : largest;
      }
      return largest;
    });
  }

  /**
   * Evaluate if a consensus group meets verification requirements
   */
  private evaluateConsensus(
    group: ConsensusGroup,
    totalSubmissions: number,
  ): Omit<ConsensusResult, 'consensusGroup' | 'totalSubmissions'> {
    // Check minimum unique wallets requirement
    if (group.uniqueWallets.size < this.config.minimumUniqueWallets) {
      return {
        isVerified: false,
        verifiedResult: null,
        reason: `Insufficient unique wallets (${group.uniqueWallets.size}/${this.config.minimumUniqueWallets})`,
      };
    }

    // Check majority threshold
    const majorityPercentage = group.count / totalSubmissions;
    if (majorityPercentage < this.config.majorityThreshold) {
      return {
        isVerified: false,
        verifiedResult: null,
        reason: `Insufficient majority (${(majorityPercentage * 100).toFixed(1)}%/${(this.config.majorityThreshold * 100)}%)`,
      };
    }

    return {
      isVerified: true,
      verifiedResult: group.results,
      reason: `Consensus achieved: ${group.uniqueWallets.size} unique wallets, ${(majorityPercentage * 100).toFixed(1)}% majority`,
    };
  }

  /**
   * Process consensus for all polling stations
   */
  processAllStations(): void {
    const allStations = dataStorage.getAllPollingStations();
    
    for (const station of allStations) {
      if (station.status === 'Pending Consensus') {
        const result = this.processConsensus(station.id);
        console.log(`Consensus result for station ${station.id}:`, result.reason);
      }
    }
  }

  /**
   * Get consensus configuration
   */
  getConfig(): ConsensusConfig {
    return {...this.config};
  }

  /**
   * Update consensus configuration
   */
  updateConfig(newConfig: Partial<ConsensusConfig>): void {
    this.config = {...this.config, ...newConfig};
    console.log('Consensus configuration updated:', this.config);
  }
}

// Create and export singleton instance
const consensusService = new ConsensusService();
export default consensusService;
