/**
 * Data Storage Service for OYAH! Backend
 * In-memory storage for <PERSON> (would use Redis or database in production)
 */

import {Submission, PollingStationData, ElectionResults} from '../types';

class DataStorageService {
  private pollingStations: Map<string, PollingStationData> = new Map();
  private submissions: Map<string, Submission> = new Map();

  /**
   * Store a new submission
   */
  storeSubmission(submission: Submission): void {
    // Store the submission
    this.submissions.set(submission.id, submission);

    // Get or create polling station data
    let stationData = this.pollingStations.get(submission.pollingStationId);
    if (!stationData) {
      stationData = {
        id: submission.pollingStationId,
        submissions: [],
        consensusGroups: new Map(),
        verifiedResult: null,
        status: 'Pending Consensus',
        lastUpdated: new Date().toISOString(),
      };
      this.pollingStations.set(submission.pollingStationId, stationData);
    }

    // Add submission to station data
    stationData.submissions.push(submission);
    stationData.lastUpdated = new Date().toISOString();

    console.log(`Stored submission ${submission.id} for station ${submission.pollingStationId}`);
  }

  /**
   * Get all submissions for a polling station
   */
  getSubmissionsForStation(pollingStationId: string): Submission[] {
    const stationData = this.pollingStations.get(pollingStationId);
    return stationData ? stationData.submissions : [];
  }

  /**
   * Get polling station data
   */
  getPollingStationData(pollingStationId: string): PollingStationData | null {
    return this.pollingStations.get(pollingStationId) || null;
  }

  /**
   * Update polling station status and verified result
   */
  updatePollingStationStatus(
    pollingStationId: string,
    status: 'Pending Consensus' | 'Verified',
    verifiedResult?: ElectionResults,
  ): void {
    const stationData = this.pollingStations.get(pollingStationId);
    if (stationData) {
      stationData.status = status;
      stationData.lastUpdated = new Date().toISOString();
      if (verifiedResult) {
        stationData.verifiedResult = verifiedResult;
      }
    }
  }

  /**
   * Get all polling stations
   */
  getAllPollingStations(): PollingStationData[] {
    return Array.from(this.pollingStations.values());
  }

  /**
   * Get national tally from all verified stations
   */
  getNationalTally(): ElectionResults {
    const tally: ElectionResults = {
      candidateA: 0,
      candidateB: 0,
      spoilt: 0,
    };

    for (const station of this.pollingStations.values()) {
      if (station.status === 'Verified' && station.verifiedResult) {
        tally.candidateA += station.verifiedResult.candidateA;
        tally.candidateB += station.verifiedResult.candidateB;
        tally.spoilt += station.verifiedResult.spoilt;
      }
    }

    return tally;
  }

  /**
   * Get statistics
   */
  getStatistics() {
    const totalStations = this.pollingStations.size;
    const verifiedStations = Array.from(this.pollingStations.values()).filter(
      station => station.status === 'Verified',
    ).length;
    const totalSubmissions = this.submissions.size;

    return {
      totalStations,
      verifiedStations,
      totalSubmissions,
      pendingStations: totalStations - verifiedStations,
    };
  }

  /**
   * Clear all data (for testing purposes)
   */
  clearAll(): void {
    this.pollingStations.clear();
    this.submissions.clear();
    console.log('All data cleared');
  }

  /**
   * Get submission by ID
   */
  getSubmission(submissionId: string): Submission | null {
    return this.submissions.get(submissionId) || null;
  }

  /**
   * Check if wallet has already submitted for a station
   */
  hasWalletSubmittedForStation(walletAddress: string, pollingStationId: string): boolean {
    const stationData = this.pollingStations.get(pollingStationId);
    if (!stationData) return false;

    return stationData.submissions.some(
      submission => submission.walletAddress === walletAddress,
    );
  }
}

// Create and export singleton instance
const dataStorage = new DataStorageService();
export default dataStorage;
