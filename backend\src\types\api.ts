/**
 * API-related type definitions for OYAH! Backend
 */

export interface GPSCoordinates {
  latitude: number;
  longitude: number;
}

export interface ElectionResults {
  candidateA: number;
  candidateB: number;
  spoilt: number;
}

export type SubmissionType = 'image_ocr' | 'audio_stt';

export interface ResultPayload {
  walletAddress: string;
  pollingStationId: string;
  gpsCoordinates: GPSCoordinates;
  timestamp: string;
  results: ElectionResults;
  submissionType: SubmissionType;
}

export interface PollingStationStatus {
  id: string;
  status: 'Pending Consensus' | 'Verified';
  results: ElectionResults | null;
  submissionCount: number;
  lastUpdated: string;
}

export interface TallyResponse {
  nationalTally: ElectionResults;
  pollingStations: PollingStationStatus[];
  totalStations: number;
  verifiedStations: number;
  lastUpdated: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
}
