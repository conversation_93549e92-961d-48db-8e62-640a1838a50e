/**
 * Consensus-related type definitions for OYAH! Backend
 */

import {ElectionResults, SubmissionType} from './api';

export interface Submission {
  id: string;
  walletAddress: string;
  pollingStationId: string;
  results: ElectionResults;
  submissionType: SubmissionType;
  timestamp: string;
  gpsCoordinates: {
    latitude: number;
    longitude: number;
  };
}

export interface ConsensusGroup {
  results: ElectionResults;
  submissions: Submission[];
  uniqueWallets: Set<string>;
  count: number;
}

export interface PollingStationData {
  id: string;
  submissions: Submission[];
  consensusGroups: Map<string, ConsensusGroup>;
  verifiedResult: ElectionResults | null;
  status: 'Pending Consensus' | 'Verified';
  lastUpdated: string;
}

export interface ConsensusConfig {
  minimumSubmissions: number;
  minimumUniqueWallets: number;
  majorityThreshold: number; // Percentage (e.g., 0.6 for 60%)
}

export interface ConsensusResult {
  isVerified: boolean;
  verifiedResult: ElectionResults | null;
  consensusGroup: ConsensusGroup | null;
  totalSubmissions: number;
  reason: string;
}
