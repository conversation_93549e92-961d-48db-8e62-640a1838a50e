/**
 * End-to-End API Testing Script
 * Tests the complete backend API functionality
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api/v1';

// Test data
const testSubmissions = [
  {
    walletAddress: '5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY',
    pollingStationId: 'KAS-01-123',
    gpsCoordinates: { latitude: -1.286389, longitude: 36.817223 },
    timestamp: new Date().toISOString(),
    results: { candidateA: 152, candidateB: 210, spoilt: 12 },
    submissionType: 'image_ocr'
  },
  {
    walletAddress: '5FHneW46xGXgs5mUiveU4sbTyGBzmstUspZC92UhjJM694ty',
    pollingStationId: 'KAS-01-123',
    gpsCoordinates: { latitude: -1.286389, longitude: 36.817223 },
    timestamp: new Date().toISOString(),
    results: { candidateA: 152, candidateB: 210, spoilt: 12 },
    submissionType: 'audio_stt'
  },
  {
    walletAddress: '5DAAnrj7VHTznn2AWBemMuyBwZWs6FNFjdyVXUeYum3PTXFy',
    pollingStationId: 'KAS-01-123',
    gpsCoordinates: { latitude: -1.286389, longitude: 36.817223 },
    timestamp: new Date().toISOString(),
    results: { candidateA: 152, candidateB: 210, spoilt: 12 },
    submissionType: 'image_ocr'
  },
  {
    walletAddress: '5HGjWAeFDfFCWPsjFQdVV2Msvz2XtMktvgocEZcCj68kUMaw',
    pollingStationId: 'KAS-01-124',
    gpsCoordinates: { latitude: -1.290000, longitude: 36.820000 },
    timestamp: new Date().toISOString(),
    results: { candidateA: 89, candidateB: 156, spoilt: 8 },
    submissionType: 'image_ocr'
  }
];

async function testHealthCheck() {
  console.log('\n🔍 Testing Health Check...');
  try {
    const response = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ Health check passed:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function testSubmitResult(submission, index) {
  console.log(`\n📤 Testing Submit Result ${index + 1}...`);
  try {
    const response = await axios.post(`${API_BASE_URL}/submitResult`, submission);
    console.log('✅ Submission successful:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Submission failed:', error.response?.data || error.message);
    return false;
  }
}

async function testGetTally() {
  console.log('\n📊 Testing Get Tally...');
  try {
    const response = await axios.get(`${API_BASE_URL}/getTally`);
    console.log('✅ Tally retrieved successfully:');
    console.log('   National Tally:', response.data.data.nationalTally);
    console.log('   Total Stations:', response.data.data.totalStations);
    console.log('   Verified Stations:', response.data.data.verifiedStations);
    console.log('   Polling Stations:', response.data.data.pollingStations.length);
    return true;
  } catch (error) {
    console.error('❌ Get tally failed:', error.response?.data || error.message);
    return false;
  }
}

async function testDuplicateSubmission() {
  console.log('\n🔄 Testing Duplicate Submission Prevention...');
  try {
    // Try to submit the same wallet address for the same station again
    const duplicateSubmission = { ...testSubmissions[0] };
    const response = await axios.post(`${API_BASE_URL}/submitResult`, duplicateSubmission);
    console.log('❌ Duplicate submission should have been rejected');
    return false;
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('✅ Duplicate submission correctly rejected:', error.response.data.message);
      return true;
    } else {
      console.error('❌ Unexpected error:', error.response?.data || error.message);
      return false;
    }
  }
}

async function testInvalidData() {
  console.log('\n❌ Testing Invalid Data Validation...');
  try {
    const invalidSubmission = {
      walletAddress: 'invalid',
      pollingStationId: '',
      results: { candidateA: -1 }
    };
    const response = await axios.post(`${API_BASE_URL}/submitResult`, invalidSubmission);
    console.log('❌ Invalid data should have been rejected');
    return false;
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Invalid data correctly rejected:', error.response.data.message);
      return true;
    } else {
      console.error('❌ Unexpected error:', error.response?.data || error.message);
      return false;
    }
  }
}

async function runTests() {
  console.log('🚀 Starting OYAH! Backend API Tests\n');
  console.log('=' .repeat(50));

  const results = [];

  // Test health check
  results.push(await testHealthCheck());

  // Test result submissions
  for (let i = 0; i < testSubmissions.length; i++) {
    results.push(await testSubmitResult(testSubmissions[i], i));
    // Small delay between submissions
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Test get tally
  results.push(await testGetTally());

  // Test duplicate submission prevention
  results.push(await testDuplicateSubmission());

  // Test invalid data validation
  results.push(await testInvalidData());

  // Summary
  console.log('\n' + '=' .repeat(50));
  console.log('📋 Test Summary:');
  const passed = results.filter(r => r).length;
  const total = results.length;
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);

  if (passed === total) {
    console.log('\n🎉 All tests passed! Backend is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the backend implementation.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
