{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/routes/*": ["routes/*"], "@/services/*": ["services/*"], "@/types/*": ["types/*"], "@/middleware/*": ["middleware/*"], "@/utils/*": ["utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}