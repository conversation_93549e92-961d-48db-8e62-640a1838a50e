<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OYAH! MVP Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            flex: 1;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-top: 40px;
        }
        
        .logo {
            font-size: 48px;
            font-weight: bold;
            color: #00d4aa;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .screen {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        
        .screen.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .button {
            background: #00d4aa;
            color: white;
            border: none;
            padding: 18px 24px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 12px;
            transition: all 0.2s;
        }
        
        .button:hover {
            background: #00b894;
            transform: translateY(-2px);
        }
        
        .button.secondary {
            background: #16213e;
            border: 2px solid #00d4aa;
        }
        
        .action-button {
            background: #16213e;
            border: 2px solid #00d4aa;
            color: white;
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 20px;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
        }
        
        .action-button:hover {
            background: #1e2a4a;
            transform: translateY(-2px);
        }
        
        .action-icon {
            font-size: 48px;
            margin-bottom: 12px;
        }
        
        .action-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .action-subtext {
            font-size: 14px;
            opacity: 0.7;
            line-height: 1.4;
        }
        
        .status {
            background: #16213e;
            padding: 16px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        .status.success {
            border: 2px solid #00d4aa;
        }
        
        .back-button {
            color: #00d4aa;
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .tally-card {
            background: #16213e;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .candidate-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        
        .candidate-votes {
            color: #00d4aa;
            font-weight: 600;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            opacity: 0.6;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Splash Screen -->
        <div id="splash" class="screen active">
            <div class="header">
                <div class="logo">OYAH!</div>
                <div class="subtitle">Decentralized Election Witness</div>
            </div>
            
            <div style="margin-top: 80px;">
                <button class="button" onclick="connectWallet()">
                    Connect Wallet to Witness
                </button>
            </div>
            
            <div class="footer">
                Secure • Transparent • Decentralized
            </div>
        </div>
        
        <!-- Main Action Screen -->
        <div id="mainAction" class="screen">
            <div class="header">
                <h2>Welcome, Witness</h2>
                <div style="font-size: 12px; color: #00d4aa; font-family: monospace;">
                    5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY
                </div>
            </div>
            
            <div style="margin: 40px 0;">
                <h3 style="text-align: center; margin-bottom: 40px;">Choose Your Witnessing Method</h3>
                
                <div class="action-button" onclick="showImageCapture()">
                    <div class="action-icon">📷</div>
                    <div class="action-text">Capture Form Image</div>
                    <div class="action-subtext">Take a photo of Form 34A for OCR processing</div>
                </div>
                
                <div class="action-button" onclick="showAudioCapture()">
                    <div class="action-icon">🎤</div>
                    <div class="action-text">Record Official Announcement</div>
                    <div class="action-subtext">Record the official results announcement</div>
                </div>
            </div>
            
            <button class="button secondary" onclick="showDashboard()">
                View Live Tally Dashboard
            </button>
        </div>
        
        <!-- Image Capture Screen -->
        <div id="imageCapture" class="screen">
            <button class="back-button" onclick="showMainAction()">← Back</button>
            <h2>Capture Form 34A</h2>
            
            <div class="status">
                <div class="action-icon">📷</div>
                <div>Position Form 34A in the frame</div>
                <div style="font-size: 14px; opacity: 0.7; margin-top: 8px;">
                    Ensure the form is well-lit and all text is clearly visible
                </div>
            </div>
            
            <button class="button" onclick="processOCR()">Capture Form 34A</button>
        </div>
        
        <!-- Audio Capture Screen -->
        <div id="audioCapture" class="screen">
            <button class="back-button" onclick="showMainAction()">← Back</button>
            <h2>Record Announcement</h2>
            
            <div class="status">
                <div class="action-icon">🎤</div>
                <div>Ready to Record</div>
                <div style="font-size: 14px; opacity: 0.7; margin-top: 8px;">
                    Tap the button below to start
                </div>
            </div>
            
            <button class="button" onclick="processSTT()">Start Recording</button>
        </div>
        
        <!-- Confirmation Screen -->
        <div id="confirmation" class="screen">
            <button class="back-button" onclick="showMainAction()">← Back</button>
            <h2>Confirm Results</h2>
            
            <div class="status success">
                <div>Data Source: <span id="dataSource">Form 34A (OCR)</span></div>
            </div>
            
            <div class="tally-card">
                <div class="candidate-row">
                    <span>Candidate A Votes</span>
                    <span class="candidate-votes">152</span>
                </div>
                <div class="candidate-row">
                    <span>Candidate B Votes</span>
                    <span class="candidate-votes">210</span>
                </div>
                <div class="candidate-row">
                    <span>Spoilt Ballots</span>
                    <span class="candidate-votes">12</span>
                </div>
                <hr style="margin: 12px 0; border: 1px solid #333;">
                <div class="candidate-row">
                    <span style="font-weight: 600;">Total Votes</span>
                    <span class="candidate-votes" style="font-size: 20px;">374</span>
                </div>
            </div>
            
            <button class="button" onclick="submitResults()">Confirm & Submit</button>
        </div>
        
        <!-- Dashboard Screen -->
        <div id="dashboard" class="screen">
            <button class="back-button" onclick="showMainAction()">← Back</button>
            <h2>Live Dashboard</h2>
            
            <div class="tally-card">
                <h3 style="margin-bottom: 16px;">National Tally</h3>
                <div class="candidate-row">
                    <span>Candidate A</span>
                    <span class="candidate-votes">1,247</span>
                </div>
                <div class="candidate-row">
                    <span>Candidate B</span>
                    <span class="candidate-votes">1,856</span>
                </div>
                <div class="candidate-row">
                    <span>Spoilt Ballots</span>
                    <span class="candidate-votes">89</span>
                </div>
                <hr style="margin: 12px 0; border: 1px solid #333;">
                <div class="candidate-row">
                    <span style="font-weight: 600;">Total Votes</span>
                    <span class="candidate-votes" style="font-size: 20px;">3,192</span>
                </div>
            </div>
            
            <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                <div class="tally-card" style="flex: 1; text-align: center;">
                    <div class="candidate-votes" style="font-size: 24px;">8</div>
                    <div style="font-size: 12px;">Verified Stations</div>
                </div>
                <div class="tally-card" style="flex: 1; text-align: center;">
                    <div class="candidate-votes" style="font-size: 24px;">12</div>
                    <div style="font-size: 12px;">Total Stations</div>
                </div>
                <div class="tally-card" style="flex: 1; text-align: center;">
                    <div class="candidate-votes" style="font-size: 24px;">67%</div>
                    <div style="font-size: 12px;">Completion</div>
                </div>
            </div>
            
            <div style="text-align: center; opacity: 0.6; font-size: 12px;">
                Last updated: Just now<br>
                Auto-refresh: 30s
            </div>
        </div>
        
        <!-- Success Screen -->
        <div id="success" class="screen">
            <div class="header">
                <div style="font-size: 64px; margin-bottom: 20px;">✅</div>
                <h2>Success!</h2>
                <div style="margin-top: 16px; opacity: 0.8;">
                    Election results submitted successfully!
                </div>
            </div>
            
            <div style="margin-top: 40px;">
                <button class="button" onclick="showDashboard()">View Dashboard</button>
                <button class="button secondary" onclick="showMainAction()">Submit Another</button>
            </div>
        </div>
    </div>
    
    <script>
        function showScreen(screenId) {
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });
            document.getElementById(screenId).classList.add('active');
        }
        
        function connectWallet() {
            setTimeout(() => {
                showScreen('mainAction');
            }, 1000);
        }
        
        function showMainAction() {
            showScreen('mainAction');
        }
        
        function showImageCapture() {
            showScreen('imageCapture');
        }
        
        function showAudioCapture() {
            showScreen('audioCapture');
        }
        
        function showDashboard() {
            showScreen('dashboard');
        }
        
        function processOCR() {
            document.getElementById('dataSource').textContent = 'Form 34A (OCR)';
            setTimeout(() => {
                showScreen('confirmation');
            }, 2000);
        }
        
        function processSTT() {
            document.getElementById('dataSource').textContent = 'Audio Recording (STT)';
            setTimeout(() => {
                showScreen('confirmation');
            }, 3000);
        }
        
        function submitResults() {
            setTimeout(() => {
                showScreen('success');
            }, 1500);
        }
    </script>
</body>
</html>
