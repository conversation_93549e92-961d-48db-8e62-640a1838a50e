# OYAH! Mobile dApp

React Native mobile application for the OYAH! decentralized election witness system.

## 🏗️ Architecture

### Technology Stack
- **React Native**: Cross-platform mobile development
- **TypeScript**: Type-safe JavaScript
- **Zustand**: Lightweight state management
- **Styled Components**: CSS-in-JS styling
- **Polkadot.js**: Web3 wallet integration
- **TensorFlow Lite**: On-device ML processing
- **React Navigation**: Screen navigation

### Directory Structure
```
src/
├── components/     # Reusable UI components
├── screens/        # Screen components
├── services/       # Business logic services
│   ├── walletService.ts    # Polkadot.js integration
│   ├── apiService.ts       # Backend API calls
│   └── mlService.ts        # ML processing
├── state/          # Zustand stores
├── navigation/     # Navigation configuration
├── types/          # TypeScript definitions
├── assets/         # Static assets
└── ml/             # TensorFlow Lite models
```

## 🚀 Setup Instructions

### Prerequisites
- Node.js v18.16.0+
- React Native development environment
- Android Studio (for Android)
- Xcode (for iOS)

### Installation
1. Install dependencies:
```bash
npm install
```

2. Install iOS dependencies (iOS only):
```bash
cd ios && pod install && cd ..
```

3. Start Metro bundler:
```bash
npm start
```

4. Run the app:
```bash
# Android
npm run android

# iOS
npm run ios
```

## 📱 Features

### Core Screens
- **SplashScreen**: Wallet connection interface
- **MainActionScreen**: Choose capture method
- **ImageCaptureScreen**: Camera for Form 34A
- **AudioCaptureScreen**: Audio recording
- **ConfirmationScreen**: Review and edit results
- **DashboardScreen**: Live election tally

### Services
- **walletService**: Nova Wallet integration
- **apiService**: Backend communication
- **mlService**: OCR and Speech-to-Text processing

### State Management
- **walletStore**: Wallet connection state
- **dashboardStore**: Dashboard data and refresh state

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the mobile directory:
```
API_BASE_URL=http://localhost:3000/api/v1
POLKADOT_WS_ENDPOINT=wss://rpc.polkadot.io
```

### TypeScript Configuration
The project uses strict TypeScript configuration with path mapping for clean imports:
```typescript
// Instead of: import Component from '../../../components/Component'
// Use: import Component from '@/components/Component'
```

## 🧪 Testing

### Run Tests
```bash
npm test
```

### Test Coverage
```bash
npm run test:coverage
```

### Linting
```bash
npm run lint
```

## 📦 Build

### Development Build
```bash
# Android
npm run android

# iOS
npm run ios
```

### Production Build
```bash
# Android
cd android && ./gradlew assembleRelease

# iOS
# Use Xcode to create archive
```

## 🔒 Security Considerations

### Wallet Integration
- Uses read-only wallet access
- No private key storage
- Secure message signing

### Data Handling
- On-device ML processing
- No sensitive data transmission
- GPS coordinates for verification

## 🐛 Troubleshooting

### Common Issues

1. **Metro bundler issues**:
```bash
npm start -- --reset-cache
```

2. **Android build issues**:
```bash
cd android && ./gradlew clean && cd ..
```

3. **iOS build issues**:
```bash
cd ios && pod install && cd ..
```

4. **TypeScript errors**:
```bash
npm run type-check
```

### Performance Tips
- Use React.memo for expensive components
- Implement proper list virtualization
- Optimize image loading and caching
- Monitor memory usage during ML processing

## 📱 Platform-Specific Notes

### Android
- Minimum SDK: 21 (Android 5.0)
- Target SDK: 33 (Android 13)
- Permissions: Camera, Microphone, Location

### iOS
- Minimum iOS: 11.0
- Permissions: Camera, Microphone, Location
- App Transport Security configured

## 🔄 State Management

### Zustand Stores
```typescript
// Wallet Store
const {isConnected, address, connectWallet} = useWalletStore();

// Dashboard Store
const {tallyData, fetchTallyData, isLoading} = useDashboardStore();
```

## 🎨 Styling

### Styled Components
```typescript
const StyledButton = styled.TouchableOpacity`
  background-color: #00d4aa;
  padding: 16px 24px;
  border-radius: 12px;
`;
```

### Theme Colors
- Primary: #00d4aa (Teal)
- Background: #1a1a2e (Dark Blue)
- Secondary: #16213e (Medium Blue)
- Text: #ffffff (White)

## 📚 Additional Resources

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Polkadot.js Documentation](https://polkadot.js.org/docs/)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [Styled Components Documentation](https://styled-components.com/docs)
