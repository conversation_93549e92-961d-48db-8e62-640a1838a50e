<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OYAH! Mobile App - React Native Web</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a2e;
            color: white;
            overflow-x: hidden;
        }
        
        #root {
            width: 100%;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        }
        
        .phone-container {
            width: 375px;
            height: 812px;
            background: #1a1a2e;
            border-radius: 25px;
            border: 8px solid #333;
            overflow: hidden;
            position: relative;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .phone-notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 25px;
            background: #333;
            border-radius: 0 0 15px 15px;
            z-index: 10;
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            padding: 30px 20px 20px 20px;
            box-sizing: border-box;
            overflow-y: auto;
        }
        
        .screen {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        
        .screen.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-top: 20px;
        }
        
        .logo {
            font-size: 48px;
            font-weight: bold;
            color: #00d4aa;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .welcome-text {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .address-text {
            font-size: 10px;
            color: #00d4aa;
            font-family: monospace;
            word-break: break-all;
        }
        
        .button {
            background: #00d4aa;
            color: white;
            border: none;
            padding: 18px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 12px;
            transition: all 0.2s;
        }
        
        .button:hover {
            background: #00b894;
            transform: translateY(-2px);
        }
        
        .button.secondary {
            background: transparent;
            border: 1px solid #00d4aa;
            color: #00d4aa;
        }
        
        .action-button {
            background: #16213e;
            border: 2px solid #00d4aa;
            color: white;
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 20px;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
        }
        
        .action-button:hover {
            background: #1e2a4a;
            transform: translateY(-2px);
        }
        
        .action-icon {
            font-size: 48px;
            margin-bottom: 12px;
        }
        
        .action-text {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .action-subtext {
            font-size: 12px;
            opacity: 0.7;
            line-height: 1.4;
        }
        
        .back-button {
            color: #00d4aa;
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 20px;
            padding: 0;
        }
        
        .screen-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .capture-container {
            background: #16213e;
            border: 2px dashed #00d4aa;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            margin: 20px 0;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .capture-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        .capture-text {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .capture-subtext {
            font-size: 12px;
            opacity: 0.7;
            line-height: 1.4;
        }
        
        .tally-card {
            background: #16213e;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .tally-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        
        .tally-value {
            color: #00d4aa;
            font-weight: 600;
        }
        
        .divider {
            height: 1px;
            background: #333;
            margin: 12px 0;
        }
        
        .total-value {
            color: #00d4aa;
            font-size: 20px;
            font-weight: bold;
        }
        
        .stats-container {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: #16213e;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            flex: 1;
        }
        
        .stat-value {
            color: #00d4aa;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 10px;
        }
        
        .footer {
            text-align: center;
            opacity: 0.6;
            font-size: 14px;
            margin-top: 40px;
        }
        
        .success-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        .success-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        
        .success-subtext {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 40px;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="phone-container">
            <div class="phone-notch"></div>
            <div class="app-container">
                <!-- Splash Screen -->
                <div id="splash" class="screen active">
                    <div class="header">
                        <div class="logo">OYAH!</div>
                        <div class="subtitle">Decentralized Election Witness</div>
                    </div>
                    
                    <div style="margin-top: 80px;">
                        <button class="button" onclick="connectWallet()">
                            <span id="connect-text">Connect Wallet to Witness</span>
                        </button>
                    </div>
                    
                    <div class="footer">
                        Secure • Transparent • Decentralized
                    </div>
                </div>
                
                <!-- Main Action Screen -->
                <div id="main" class="screen">
                    <div class="header">
                        <div class="welcome-text">Welcome, Witness</div>
                        <div class="address-text">5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY</div>
                    </div>
                    
                    <div style="margin: 40px 0;">
                        <h3 style="text-align: center; margin-bottom: 40px; font-size: 18px;">Choose Your Witnessing Method</h3>
                        
                        <div class="action-button" onclick="showScreen('image')">
                            <div class="action-icon">📷</div>
                            <div class="action-text">Capture Form Image</div>
                            <div class="action-subtext">Take a photo of Form 34A for OCR processing</div>
                        </div>
                        
                        <div class="action-button" onclick="showScreen('audio')">
                            <div class="action-icon">🎤</div>
                            <div class="action-text">Record Official Announcement</div>
                            <div class="action-subtext">Record the official results announcement</div>
                        </div>
                    </div>
                    
                    <button class="button secondary" onclick="showScreen('dashboard')">
                        View Live Tally Dashboard
                    </button>
                </div>
                
                <!-- Image Capture Screen -->
                <div id="image" class="screen">
                    <button class="back-button" onclick="showScreen('main')">← Back</button>
                    <div class="screen-title">Capture Form 34A</div>
                    
                    <div class="capture-container">
                        <div class="capture-icon">📷</div>
                        <div class="capture-text">Position Form 34A in the frame</div>
                        <div class="capture-subtext">Ensure the form is well-lit and all text is clearly visible</div>
                    </div>
                    
                    <button class="button" onclick="processOCR()">Capture Form 34A</button>
                </div>
                
                <!-- Audio Capture Screen -->
                <div id="audio" class="screen">
                    <button class="back-button" onclick="showScreen('main')">← Back</button>
                    <div class="screen-title">Record Announcement</div>
                    
                    <div class="capture-container">
                        <div class="capture-icon">🎤</div>
                        <div class="capture-text">Ready to Record</div>
                        <div class="capture-subtext">Tap the button below to start</div>
                    </div>
                    
                    <button class="button" onclick="processSTT()">Start Recording</button>
                </div>
                
                <!-- Confirmation Screen -->
                <div id="confirm" class="screen">
                    <button class="back-button" onclick="showScreen('main')">← Back</button>
                    <div class="screen-title">Confirm Results</div>
                    
                    <div style="background: #16213e; border: 2px solid #00d4aa; padding: 16px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
                        <div>Data Source: <span id="data-source">Form 34A (OCR)</span></div>
                    </div>
                    
                    <div class="tally-card">
                        <div class="tally-row">
                            <span>Candidate A Votes</span>
                            <span class="tally-value">152</span>
                        </div>
                        <div class="tally-row">
                            <span>Candidate B Votes</span>
                            <span class="tally-value">210</span>
                        </div>
                        <div class="tally-row">
                            <span>Spoilt Ballots</span>
                            <span class="tally-value">12</span>
                        </div>
                        <div class="divider"></div>
                        <div class="tally-row">
                            <span style="font-weight: 600;">Total Votes</span>
                            <span class="total-value">374</span>
                        </div>
                    </div>
                    
                    <button class="button" onclick="submitResults()">Confirm & Submit</button>
                </div>
                
                <!-- Dashboard Screen -->
                <div id="dashboard" class="screen">
                    <button class="back-button" onclick="showScreen('main')">← Back</button>
                    <div class="screen-title">Live Dashboard</div>
                    
                    <div class="tally-card">
                        <h3 style="margin-bottom: 16px; font-size: 16px;">National Tally</h3>
                        <div class="tally-row">
                            <span>Candidate A</span>
                            <span class="tally-value">1,247</span>
                        </div>
                        <div class="tally-row">
                            <span>Candidate B</span>
                            <span class="tally-value">1,856</span>
                        </div>
                        <div class="tally-row">
                            <span>Spoilt Ballots</span>
                            <span class="tally-value">89</span>
                        </div>
                        <div class="divider"></div>
                        <div class="tally-row">
                            <span style="font-weight: 600;">Total Votes</span>
                            <span class="total-value">3,192</span>
                        </div>
                    </div>
                    
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-value">8</div>
                            <div class="stat-label">Verified Stations</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">12</div>
                            <div class="stat-label">Total Stations</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">67%</div>
                            <div class="stat-label">Completion</div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; opacity: 0.6; font-size: 12px;">
                        Last updated: Just now<br>
                        Auto-refresh: 30s
                    </div>
                </div>
                
                <!-- Success Screen -->
                <div id="success" class="screen">
                    <div class="header">
                        <div class="success-icon">✅</div>
                        <div class="success-title">Success!</div>
                        <div class="success-subtext">Election results submitted successfully!</div>
                    </div>
                    
                    <div>
                        <button class="button" onclick="showScreen('dashboard')">View Dashboard</button>
                        <button class="button secondary" onclick="showScreen('main')">Submit Another</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showScreen(screenId) {
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });
            document.getElementById(screenId).classList.add('active');
        }
        
        function connectWallet() {
            document.getElementById('connect-text').textContent = 'Connecting...';
            setTimeout(() => {
                showScreen('main');
            }, 1000);
        }
        
        function processOCR() {
            document.getElementById('data-source').textContent = 'Form 34A (OCR)';
            setTimeout(() => {
                showScreen('confirm');
            }, 2000);
        }
        
        function processSTT() {
            document.getElementById('data-source').textContent = 'Audio Recording (STT)';
            setTimeout(() => {
                showScreen('confirm');
            }, 3000);
        }
        
        function submitResults() {
            setTimeout(() => {
                showScreen('success');
            }, 1500);
        }
    </script>
</body>
</html>
