{"name": "oyah-mobile", "version": "1.0.0", "description": "OYAH! Decentralized Election Witness Mobile dApp", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@polkadot/api": "^10.9.1", "@polkadot/extension-dapp": "^0.46.5", "@polkadot/util": "^12.4.2", "@polkadot/util-crypto": "^12.4.2", "zustand": "^4.4.1", "styled-components": "^5.3.11", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "react-native-screens": "^3.22.1", "react-native-safe-area-context": "^4.7.1", "react-native-gesture-handler": "^2.12.1", "react-native-fs": "^2.20.0", "axios": "^1.5.0", "@react-native-async-storage/async-storage": "^1.19.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "@types/styled-components": "^5.1.26", "@types/styled-components-react-native": "^5.2.1", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18.16.0"}}