/**
 * Main App Component for OYAH! dApp
 * Expo-compatible entry point for the election witness app
 */

import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ScrollView} from 'react-native';
import {StatusBar} from 'expo-status-bar';

type Screen = 'splash' | 'main' | 'image' | 'audio' | 'confirm' | 'dashboard' | 'success';

const App: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<Screen>('splash');
  const [walletConnected, setWalletConnected] = useState(false);

  const connectWallet = () => {
    setWalletConnected(true);
    setTimeout(() => setCurrentScreen('main'), 1000);
  };

  const renderSplashScreen = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.logo}>OYAH!</Text>
        <Text style={styles.subtitle}>Decentralized Election Witness</Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={connectWallet}>
          <Text style={styles.buttonText}>
            {walletConnected ? 'Connecting...' : 'Connect Wallet to Witness'}
          </Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.footer}>Secure • Transparent • Decentralized</Text>
    </View>
  );

  const renderMainScreen = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.welcomeText}>Welcome, Witness</Text>
        <Text style={styles.addressText}>5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY</Text>
      </View>

      <View style={styles.actionsContainer}>
        <Text style={styles.actionTitle}>Choose Your Witnessing Method</Text>

        <TouchableOpacity style={styles.actionButton} onPress={() => setCurrentScreen('image')}>
          <Text style={styles.actionIcon}>📷</Text>
          <Text style={styles.actionText}>Capture Form Image</Text>
          <Text style={styles.actionSubtext}>Take a photo of Form 34A for OCR processing</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={() => setCurrentScreen('audio')}>
          <Text style={styles.actionIcon}>🎤</Text>
          <Text style={styles.actionText}>Record Official Announcement</Text>
          <Text style={styles.actionSubtext}>Record the official results announcement</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity style={styles.dashboardButton} onPress={() => setCurrentScreen('dashboard')}>
        <Text style={styles.dashboardButtonText}>View Live Tally Dashboard</Text>
      </TouchableOpacity>
    </View>
  );

  const renderImageScreen = () => (
    <View style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={() => setCurrentScreen('main')}>
        <Text style={styles.backButtonText}>← Back</Text>
      </TouchableOpacity>

      <Text style={styles.screenTitle}>Capture Form 34A</Text>

      <View style={styles.captureContainer}>
        <Text style={styles.captureIcon}>📷</Text>
        <Text style={styles.captureText}>Position Form 34A in the frame</Text>
        <Text style={styles.captureSubtext}>Ensure the form is well-lit and all text is clearly visible</Text>
      </View>

      <TouchableOpacity style={styles.button} onPress={() => {
        setTimeout(() => setCurrentScreen('confirm'), 2000);
      }}>
        <Text style={styles.buttonText}>Capture Form 34A</Text>
      </TouchableOpacity>
    </View>
  );

  const renderAudioScreen = () => (
    <View style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={() => setCurrentScreen('main')}>
        <Text style={styles.backButtonText}>← Back</Text>
      </TouchableOpacity>

      <Text style={styles.screenTitle}>Record Announcement</Text>

      <View style={styles.captureContainer}>
        <Text style={styles.captureIcon}>🎤</Text>
        <Text style={styles.captureText}>Ready to Record</Text>
        <Text style={styles.captureSubtext}>Tap the button below to start</Text>
      </View>

      <TouchableOpacity style={styles.button} onPress={() => {
        setTimeout(() => setCurrentScreen('confirm'), 3000);
      }}>
        <Text style={styles.buttonText}>Start Recording</Text>
      </TouchableOpacity>
    </View>
  );

  const renderConfirmScreen = () => (
    <ScrollView style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={() => setCurrentScreen('main')}>
        <Text style={styles.backButtonText}>← Back</Text>
      </TouchableOpacity>

      <Text style={styles.screenTitle}>Confirm Results</Text>

      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>Data Source: Form 34A (OCR)</Text>
      </View>

      <View style={styles.tallyCard}>
        <View style={styles.tallyRow}>
          <Text style={styles.tallyLabel}>Candidate A Votes</Text>
          <Text style={styles.tallyValue}>152</Text>
        </View>
        <View style={styles.tallyRow}>
          <Text style={styles.tallyLabel}>Candidate B Votes</Text>
          <Text style={styles.tallyValue}>210</Text>
        </View>
        <View style={styles.tallyRow}>
          <Text style={styles.tallyLabel}>Spoilt Ballots</Text>
          <Text style={styles.tallyValue}>12</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.tallyRow}>
          <Text style={styles.totalLabel}>Total Votes</Text>
          <Text style={styles.totalValue}>374</Text>
        </View>
      </View>

      <TouchableOpacity style={styles.button} onPress={() => {
        setTimeout(() => setCurrentScreen('success'), 1500);
      }}>
        <Text style={styles.buttonText}>Confirm & Submit</Text>
      </TouchableOpacity>
    </ScrollView>
  );

  const renderDashboardScreen = () => (
    <ScrollView style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={() => setCurrentScreen('main')}>
        <Text style={styles.backButtonText}>← Back</Text>
      </TouchableOpacity>

      <Text style={styles.screenTitle}>Live Dashboard</Text>

      <View style={styles.tallyCard}>
        <Text style={styles.sectionTitle}>National Tally</Text>
        <View style={styles.tallyRow}>
          <Text style={styles.tallyLabel}>Candidate A</Text>
          <Text style={styles.tallyValue}>1,247</Text>
        </View>
        <View style={styles.tallyRow}>
          <Text style={styles.tallyLabel}>Candidate B</Text>
          <Text style={styles.tallyValue}>1,856</Text>
        </View>
        <View style={styles.tallyRow}>
          <Text style={styles.tallyLabel}>Spoilt Ballots</Text>
          <Text style={styles.tallyValue}>89</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.tallyRow}>
          <Text style={styles.totalLabel}>Total Votes</Text>
          <Text style={styles.totalValue}>3,192</Text>
        </View>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>8</Text>
          <Text style={styles.statLabel}>Verified Stations</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>12</Text>
          <Text style={styles.statLabel}>Total Stations</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>67%</Text>
          <Text style={styles.statLabel}>Completion</Text>
        </View>
      </View>

      <Text style={styles.updateText}>Last updated: Just now{'\n'}Auto-refresh: 30s</Text>
    </ScrollView>
  );

  const renderSuccessScreen = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.successIcon}>✅</Text>
        <Text style={styles.successTitle}>Success!</Text>
        <Text style={styles.successSubtext}>Election results submitted successfully!</Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={() => setCurrentScreen('dashboard')}>
          <Text style={styles.buttonText}>View Dashboard</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.secondaryButton} onPress={() => setCurrentScreen('main')}>
          <Text style={styles.secondaryButtonText}>Submit Another</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'splash': return renderSplashScreen();
      case 'main': return renderMainScreen();
      case 'image': return renderImageScreen();
      case 'audio': return renderAudioScreen();
      case 'confirm': return renderConfirmScreen();
      case 'dashboard': return renderDashboardScreen();
      case 'success': return renderSuccessScreen();
      default: return renderSplashScreen();
    }
  };

  return (
    <View style={styles.app}>
      <StatusBar style="light" backgroundColor="#1a1a2e" />
      {renderCurrentScreen()}
    </View>
  );
};

const styles = StyleSheet.create({
  app: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 40,
  },
  logo: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#00d4aa',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.8,
    textAlign: 'center',
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  addressText: {
    fontSize: 12,
    color: '#00d4aa',
    fontFamily: 'monospace',
  },
  buttonContainer: {
    marginBottom: 40,
  },
  button: {
    backgroundColor: '#00d4aa',
    padding: 18,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#00d4aa',
    padding: 18,
    borderRadius: 12,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#00d4aa',
    fontSize: 18,
    fontWeight: '600',
  },
  footer: {
    color: '#ffffff',
    fontSize: 14,
    opacity: 0.6,
    textAlign: 'center',
  },
  actionsContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  actionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 40,
  },
  actionButton: {
    backgroundColor: '#16213e',
    borderWidth: 2,
    borderColor: '#00d4aa',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    alignItems: 'center',
  },
  actionIcon: {
    fontSize: 48,
    marginBottom: 12,
  },
  actionText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  actionSubtext: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
  },
  dashboardButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#00d4aa',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  dashboardButtonText: {
    color: '#00d4aa',
    fontSize: 16,
    fontWeight: '500',
  },
  backButton: {
    marginBottom: 20,
  },
  backButtonText: {
    color: '#00d4aa',
    fontSize: 16,
  },
  screenTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20,
  },
  captureContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#16213e',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#00d4aa',
    borderStyle: 'dashed',
    margin: 20,
    padding: 40,
  },
  captureIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  captureText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  captureSubtext: {
    color: '#ffffff',
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
  },
  statusContainer: {
    backgroundColor: '#16213e',
    borderWidth: 2,
    borderColor: '#00d4aa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    alignItems: 'center',
  },
  statusText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  tallyCard: {
    backgroundColor: '#16213e',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  tallyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  tallyLabel: {
    color: '#ffffff',
    fontSize: 16,
  },
  tallyValue: {
    color: '#00d4aa',
    fontSize: 18,
    fontWeight: '600',
  },
  divider: {
    height: 1,
    backgroundColor: '#333',
    marginVertical: 12,
  },
  totalLabel: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
  totalValue: {
    color: '#00d4aa',
    fontSize: 20,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: '#16213e',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  statValue: {
    color: '#00d4aa',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    color: '#ffffff',
    fontSize: 12,
    textAlign: 'center',
  },
  updateText: {
    color: '#666',
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 20,
  },
  successIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 16,
  },
  successSubtext: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.8,
    textAlign: 'center',
  },
});

export default App;
