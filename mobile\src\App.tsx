/**
 * Main App Component for OYAH! dApp
 * Entry point that sets up navigation and global providers
 */

import React, {useEffect} from 'react';
import {StatusBar} from 'react-native';
import 'react-native-gesture-handler';
import AppNavigator from '@/navigation/AppNavigator';
import ErrorBoundary from '@/components/ErrorBoundary';
import {initializeApi} from '@/services/walletService';

const App: React.FC = () => {
  useEffect(() => {
    // Initialize Polkadot API on app start
    const initApi = async () => {
      try {
        await initializeApi();
        console.log('Polkadot API initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Polkadot API:', error);
      }
    };

    initApi();
  }, []);

  return (
    <ErrorBoundary>
      <StatusBar
        barStyle="light-content"
        backgroundColor="#1a1a2e"
        translucent={false}
      />
      <AppNavigator />
    </ErrorBoundary>
  );
};

export default App;
