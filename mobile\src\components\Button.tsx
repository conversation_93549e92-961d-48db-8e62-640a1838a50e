/**
 * Reusable Button Component
 */

import React from 'react';
import styled from 'styled-components/native';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  disabled?: boolean;
  loading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  disabled = false,
  loading = false,
}) => {
  return (
    <StyledButton
      variant={variant}
      disabled={disabled || loading}
      onPress={onPress}>
      <ButtonText variant={variant} disabled={disabled || loading}>
        {loading ? 'Loading...' : title}
      </ButtonText>
    </StyledButton>
  );
};

const StyledButton = styled.TouchableOpacity<{
  variant: string;
  disabled: boolean;
}>`
  padding: 16px 24px;
  border-radius: 12px;
  align-items: center;
  justify-content: center;
  background-color: ${props => {
    if (props.disabled) return '#555';
    switch (props.variant) {
      case 'primary':
        return '#00d4aa';
      case 'secondary':
        return '#16213e';
      case 'outline':
        return 'transparent';
      default:
        return '#00d4aa';
    }
  }};
  border: ${props => {
    switch (props.variant) {
      case 'outline':
        return '1px solid #00d4aa';
      default:
        return 'none';
    }
  }};
  opacity: ${props => (props.disabled ? 0.6 : 1)};
`;

const ButtonText = styled.Text<{variant: string; disabled: boolean}>`
  font-size: 16px;
  font-weight: 600;
  color: ${props => {
    if (props.disabled) return '#999';
    switch (props.variant) {
      case 'primary':
        return '#ffffff';
      case 'secondary':
        return '#ffffff';
      case 'outline':
        return '#00d4aa';
      default:
        return '#ffffff';
    }
  }};
`;

export default Button;
