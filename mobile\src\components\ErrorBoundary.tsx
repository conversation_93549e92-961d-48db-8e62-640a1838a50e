/**
 * Error Boundary Component
 * Catches and handles React component errors gracefully
 */

import React, {Component, ReactNode} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import styled from 'styled-components/native';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {hasError: false};
  }

  static getDerivedStateFromError(error: Error): State {
    return {hasError: true, error};
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  handleReset = () => {
    this.setState({hasError: false, error: undefined});
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ErrorContainer>
          <ErrorIcon>⚠️</ErrorIcon>
          <ErrorTitle>Something went wrong</ErrorTitle>
          <ErrorMessage>
            An unexpected error occurred. Please try again.
          </ErrorMessage>
          {__DEV__ && this.state.error && (
            <ErrorDetails>
              {this.state.error.message}
            </ErrorDetails>
          )}
          <RetryButton onPress={this.handleReset}>
            <RetryButtonText>Try Again</RetryButtonText>
          </RetryButton>
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

const ErrorContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #1a1a2e;
`;

const ErrorIcon = styled.Text`
  font-size: 64px;
  margin-bottom: 20px;
`;

const ErrorTitle = styled.Text`
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 12px;
  text-align: center;
`;

const ErrorMessage = styled.Text`
  font-size: 16px;
  color: #ffffff;
  opacity: 0.8;
  text-align: center;
  margin-bottom: 20px;
  line-height: 24px;
`;

const ErrorDetails = styled.Text`
  font-size: 12px;
  color: #ff6b6b;
  font-family: monospace;
  text-align: center;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #2a1f1f;
  border-radius: 8px;
`;

const RetryButton = styled.TouchableOpacity`
  background-color: #00d4aa;
  padding: 12px 24px;
  border-radius: 8px;
`;

const RetryButtonText = styled.Text`
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
`;

export default ErrorBoundary;
