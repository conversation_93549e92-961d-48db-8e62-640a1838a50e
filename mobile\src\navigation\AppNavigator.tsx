/**
 * Main App Navigator
 * Configures navigation structure for the OYAH! dApp
 */

import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {RootStackParamList} from '@/types';

// Import screens
import SplashScreen from '@/screens/SplashScreen';
import MainActionScreen from '@/screens/MainActionScreen';
import ImageCaptureScreen from '@/screens/ImageCaptureScreen';
import AudioCaptureScreen from '@/screens/AudioCaptureScreen';
import ConfirmationScreen from '@/screens/ConfirmationScreen';
import DashboardScreen from '@/screens/DashboardScreen';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Splash"
        screenOptions={{
          headerShown: false,
          cardStyle: {backgroundColor: '#1a1a2e'},
        }}>
        <Stack.Screen name="Splash" component={SplashScreen} />
        <Stack.Screen name="MainAction" component={MainActionScreen} />
        <Stack.Screen name="ImageCapture" component={ImageCaptureScreen} />
        <Stack.Screen name="AudioCapture" component={AudioCaptureScreen} />
        <Stack.Screen name="Confirmation" component={ConfirmationScreen} />
        <Stack.Screen name="Dashboard" component={DashboardScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
