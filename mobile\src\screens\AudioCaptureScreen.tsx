/**
 * Audio Capture Screen Component
 * Screen for recording official election result announcements
 */

import React, {useState, useEffect} from 'react';
import {Alert} from 'react-native';
import styled from 'styled-components/native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/types';
import {processAudioSTT} from '@/services/mlService';
import Button from '@/components/Button';

type AudioCaptureScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'AudioCapture'
>;

const AudioCaptureScreen: React.FC = () => {
  const navigation = useNavigation<AudioCaptureScreenNavigationProp>();
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [audioUri, setAudioUri] = useState<string | null>(null);

  // Timer for recording duration
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isRecording]);

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartRecording = async () => {
    try {
      // For MVP: Mock audio recording functionality
      // In production, this would use react-native-audio-recorder-player
      console.log('Starting audio recording...');
      
      setIsRecording(true);
      setRecordingDuration(0);
      setAudioUri(null);
    } catch (error) {
      Alert.alert('Error', 'Failed to start recording. Please try again.');
    }
  };

  const handleStopRecording = async () => {
    try {
      console.log('Stopping audio recording...');
      
      setIsRecording(false);
      
      // Simulate recorded audio file
      const mockAudioUri = `file://mock-audio-${Date.now()}.wav`;
      setAudioUri(mockAudioUri);
      
      Alert.alert(
        'Recording Complete',
        `Audio recorded successfully (${formatDuration(recordingDuration)}). Process for Speech-to-Text?`,
        [
          {text: 'Re-record', onPress: handleDiscardRecording},
          {text: 'Process', onPress: () => handleProcessAudio(mockAudioUri)},
        ],
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to stop recording. Please try again.');
      setIsRecording(false);
    }
  };

  const handleDiscardRecording = () => {
    setAudioUri(null);
    setRecordingDuration(0);
  };

  const handleProcessAudio = async (uri: string) => {
    setIsProcessing(true);
    
    try {
      console.log('Processing audio with STT...');
      const results = await processAudioSTT(uri);
      
      // Navigate to confirmation screen with extracted results
      navigation.navigate('Confirmation', {
        results,
        submissionType: 'audio_stt',
      });
    } catch (error) {
      Alert.alert(
        'Processing Error',
        'Failed to extract data from audio. Please try again.',
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGoBack = () => {
    if (isRecording) {
      Alert.alert(
        'Recording in Progress',
        'Stop recording before going back?',
        [
          {text: 'Cancel', style: 'cancel'},
          {text: 'Stop & Go Back', onPress: () => {
            setIsRecording(false);
            navigation.goBack();
          }},
        ],
      );
    } else {
      navigation.goBack();
    }
  };

  return (
    <Container>
      <Header>
        <BackButton onPress={handleGoBack}>
          <BackButtonText>← Back</BackButtonText>
        </BackButton>
        <Title>Record Announcement</Title>
      </Header>

      <RecordingContainer>
        <RecordingVisualizer>
          <MicrophoneIcon isRecording={isRecording}>🎤</MicrophoneIcon>
          
          {isRecording && (
            <RecordingIndicator>
              <RecordingDot />
              <RecordingText>Recording...</RecordingText>
            </RecordingIndicator>
          )}
          
          {audioUri && !isRecording && (
            <CompletedIndicator>
              <CompletedText>Recording Complete</CompletedText>
              <DurationText>{formatDuration(recordingDuration)}</DurationText>
            </CompletedIndicator>
          )}
          
          {!isRecording && !audioUri && (
            <ReadyIndicator>
              <ReadyText>Ready to Record</ReadyText>
              <ReadySubtext>Tap the button below to start</ReadySubtext>
            </ReadyIndicator>
          )}
        </RecordingVisualizer>

        {isRecording && (
          <DurationDisplay>
            <DurationText>{formatDuration(recordingDuration)}</DurationText>
          </DurationDisplay>
        )}
      </RecordingContainer>

      <ControlsContainer>
        {audioUri ? (
          <>
            <Button
              title="Record Again"
              onPress={handleDiscardRecording}
              variant="outline"
            />
            <Button
              title={isProcessing ? 'Processing...' : 'Process with STT'}
              onPress={() => handleProcessAudio(audioUri)}
              loading={isProcessing}
              disabled={isProcessing}
            />
          </>
        ) : (
          <Button
            title={isRecording ? 'Stop Recording' : 'Start Recording'}
            onPress={isRecording ? handleStopRecording : handleStartRecording}
            variant={isRecording ? 'secondary' : 'primary'}
          />
        )}
      </ControlsContainer>

      <InstructionsContainer>
        <InstructionsTitle>Instructions:</InstructionsTitle>
        <InstructionText>• Speak clearly and at normal pace</InstructionText>
        <InstructionText>• Ensure quiet environment</InstructionText>
        <InstructionText>• Include candidate names and vote counts</InstructionText>
        <InstructionText>• Mention spoilt ballots count</InstructionText>
      </InstructionsContainer>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: #1a1a2e;
`;

const Header = styled.View`
  flex-direction: row;
  align-items: center;
  padding: 20px;
  padding-top: 40px;
`;

const BackButton = styled.TouchableOpacity`
  margin-right: 16px;
`;

const BackButtonText = styled.Text`
  color: #00d4aa;
  font-size: 16px;
`;

const Title = styled.Text`
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
`;

const RecordingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 40px;
`;

const RecordingVisualizer = styled.View`
  align-items: center;
  margin-bottom: 40px;
`;

const MicrophoneIcon = styled.Text<{isRecording: boolean}>`
  font-size: 80px;
  margin-bottom: 20px;
  opacity: ${props => (props.isRecording ? 1 : 0.6)};
`;

const RecordingIndicator = styled.View`
  flex-direction: row;
  align-items: center;
`;

const RecordingDot = styled.View`
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background-color: #ff4444;
  margin-right: 8px;
`;

const RecordingText = styled.Text`
  color: #ff4444;
  font-size: 18px;
  font-weight: 600;
`;

const CompletedIndicator = styled.View`
  align-items: center;
`;

const CompletedText = styled.Text`
  color: #00d4aa;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
`;

const ReadyIndicator = styled.View`
  align-items: center;
`;

const ReadyText = styled.Text`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
`;

const ReadySubtext = styled.Text`
  color: #ffffff;
  font-size: 14px;
  opacity: 0.7;
`;

const DurationDisplay = styled.View`
  align-items: center;
`;

const DurationText = styled.Text`
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
  font-family: monospace;
`;

const ControlsContainer = styled.View`
  padding: 20px;
  gap: 12px;
`;

const InstructionsContainer = styled.View`
  padding: 20px;
  background-color: #16213e;
`;

const InstructionsTitle = styled.Text`
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
`;

const InstructionText = styled.Text`
  color: #ffffff;
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 4px;
`;

export default AudioCaptureScreen;
