/**
 * Confirmation Screen Component
 * Screen for reviewing and editing extracted election results before submission
 */

import React, {useState, useEffect} from 'react';
import {Alert, TextInput} from 'react-native';
import styled from 'styled-components/native';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList, ElectionResults, ResultPayload} from '@/types';
import {useWalletStore} from '@/state/walletStore';
import {submitResult} from '@/services/apiService';
import Button from '@/components/Button';

type ConfirmationScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Confirmation'
>;

type ConfirmationScreenRouteProp = RouteProp<RootStackParamList, 'Confirmation'>;

const ConfirmationScreen: React.FC = () => {
  const navigation = useNavigation<ConfirmationScreenNavigationProp>();
  const route = useRoute<ConfirmationScreenRouteProp>();
  const {address} = useWalletStore();
  
  const {results: initialResults, submissionType} = route.params;
  
  const [results, setResults] = useState<ElectionResults>(initialResults);
  const [pollingStationId, setPollingStationId] = useState('KAS-01-123'); // Default for MVP
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleResultChange = (field: keyof ElectionResults, value: string) => {
    const numValue = parseInt(value, 10) || 0;
    setResults(prev => ({
      ...prev,
      [field]: numValue,
    }));
  };

  const validateResults = (): boolean => {
    if (results.candidateA < 0 || results.candidateB < 0 || results.spoilt < 0) {
      Alert.alert('Invalid Input', 'Vote counts cannot be negative.');
      return false;
    }

    const total = results.candidateA + results.candidateB + results.spoilt;
    if (total === 0) {
      Alert.alert('Invalid Input', 'Total votes cannot be zero.');
      return false;
    }

    if (!pollingStationId.trim()) {
      Alert.alert('Invalid Input', 'Polling station ID is required.');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateResults()) return;
    if (!address) {
      Alert.alert('Error', 'Wallet not connected.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create payload
      const payload: ResultPayload = {
        walletAddress: address,
        pollingStationId: pollingStationId.trim(),
        gpsCoordinates: {
          latitude: -1.286389, // Mock GPS coordinates for MVP
          longitude: 36.817223,
        },
        timestamp: new Date().toISOString(),
        results,
        submissionType,
      };

      // Submit to backend
      await submitResult(payload);

      Alert.alert(
        'Success',
        'Election results submitted successfully!',
        [
          {
            text: 'View Dashboard',
            onPress: () => navigation.navigate('Dashboard'),
          },
          {
            text: 'Submit Another',
            onPress: () => navigation.navigate('MainAction'),
          },
        ],
      );
    } catch (error) {
      Alert.alert(
        'Submission Failed',
        error instanceof Error ? error.message : 'Unknown error occurred',
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoBack = () => {
    Alert.alert(
      'Discard Changes',
      'Are you sure you want to go back? Your changes will be lost.',
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'Go Back', onPress: () => navigation.goBack()},
      ],
    );
  };

  const totalVotes = results.candidateA + results.candidateB + results.spoilt;

  return (
    <Container>
      <Header>
        <BackButton onPress={handleGoBack}>
          <BackButtonText>← Back</BackButtonText>
        </BackButton>
        <Title>Confirm Results</Title>
      </Header>

      <ScrollContainer>
        <SourceInfo>
          <SourceLabel>Data Source:</SourceLabel>
          <SourceText>
            {submissionType === 'image_ocr' ? 'Form 34A (OCR)' : 'Audio Recording (STT)'}
          </SourceText>
        </SourceInfo>

        <FormSection>
          <SectionTitle>Polling Station</SectionTitle>
          <InputContainer>
            <InputLabel>Station ID</InputLabel>
            <StyledTextInput
              value={pollingStationId}
              onChangeText={setPollingStationId}
              placeholder="Enter polling station ID"
              placeholderTextColor="#666"
            />
          </InputContainer>
        </FormSection>

        <FormSection>
          <SectionTitle>Election Results</SectionTitle>
          
          <InputContainer>
            <InputLabel>Candidate A Votes</InputLabel>
            <StyledTextInput
              value={results.candidateA.toString()}
              onChangeText={(value) => handleResultChange('candidateA', value)}
              keyboardType="numeric"
              placeholder="0"
              placeholderTextColor="#666"
            />
          </InputContainer>

          <InputContainer>
            <InputLabel>Candidate B Votes</InputLabel>
            <StyledTextInput
              value={results.candidateB.toString()}
              onChangeText={(value) => handleResultChange('candidateB', value)}
              keyboardType="numeric"
              placeholder="0"
              placeholderTextColor="#666"
            />
          </InputContainer>

          <InputContainer>
            <InputLabel>Spoilt Ballots</InputLabel>
            <StyledTextInput
              value={results.spoilt.toString()}
              onChangeText={(value) => handleResultChange('spoilt', value)}
              keyboardType="numeric"
              placeholder="0"
              placeholderTextColor="#666"
            />
          </InputContainer>

          <TotalContainer>
            <TotalLabel>Total Votes:</TotalLabel>
            <TotalValue>{totalVotes}</TotalValue>
          </TotalContainer>
        </FormSection>

        <WarningContainer>
          <WarningText>
            ⚠️ Please verify all numbers are correct before submitting. 
            This data will be permanently recorded on the blockchain.
          </WarningText>
        </WarningContainer>
      </ScrollContainer>

      <ButtonContainer>
        <Button
          title={isSubmitting ? 'Submitting...' : 'Confirm & Submit'}
          onPress={handleSubmit}
          loading={isSubmitting}
          disabled={isSubmitting}
        />
      </ButtonContainer>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: #1a1a2e;
`;

const Header = styled.View`
  flex-direction: row;
  align-items: center;
  padding: 20px;
  padding-top: 40px;
`;

const BackButton = styled.TouchableOpacity`
  margin-right: 16px;
`;

const BackButtonText = styled.Text`
  color: #00d4aa;
  font-size: 16px;
`;

const Title = styled.Text`
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
`;

const ScrollContainer = styled.ScrollView`
  flex: 1;
  padding: 0 20px;
`;

const SourceInfo = styled.View`
  background-color: #16213e;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  flex-direction: row;
  align-items: center;
`;

const SourceLabel = styled.Text`
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  margin-right: 8px;
`;

const SourceText = styled.Text`
  color: #00d4aa;
  font-size: 14px;
`;

const FormSection = styled.View`
  margin-bottom: 24px;
`;

const SectionTitle = styled.Text`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
`;

const InputContainer = styled.View`
  margin-bottom: 16px;
`;

const InputLabel = styled.Text`
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
`;

const StyledTextInput = styled.TextInput`
  background-color: #16213e;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 16px;
`;

const TotalContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #16213e;
  padding: 16px;
  border-radius: 8px;
  margin-top: 8px;
`;

const TotalLabel = styled.Text`
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
`;

const TotalValue = styled.Text`
  color: #00d4aa;
  font-size: 18px;
  font-weight: bold;
`;

const WarningContainer = styled.View`
  background-color: #2a1f1f;
  border: 1px solid #ff6b6b;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
`;

const WarningText = styled.Text`
  color: #ff6b6b;
  font-size: 14px;
  line-height: 20px;
`;

const ButtonContainer = styled.View`
  padding: 20px;
`;

export default ConfirmationScreen;
