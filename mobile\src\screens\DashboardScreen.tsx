/**
 * Dashboard Screen Component
 * Live tally dashboard showing real-time election results
 */

import React, {useState, useEffect, useCallback} from 'react';
import {RefreshControl, Alert} from 'react-native';
import styled from 'styled-components/native';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList, TallyResponse, PollingStationStatus} from '@/types';
import {getTally} from '@/services/apiService';

type DashboardScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Dashboard'
>;

const DashboardScreen: React.FC = () => {
  const navigation = useNavigation<DashboardScreenNavigationProp>();
  const [tallyData, setTallyData] = useState<TallyResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string>('');

  const fetchTallyData = async (showLoading = true) => {
    try {
      if (showLoading) setIsLoading(true);
      
      const data = await getTally();
      setTallyData(data);
      setLastUpdated(new Date().toLocaleTimeString());
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to fetch election data. Please check your connection.',
      );
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    fetchTallyData(false);
  }, []);

  // Fetch data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      fetchTallyData();
    }, []),
  );

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isLoading && !isRefreshing) {
        fetchTallyData(false);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isLoading, isRefreshing]);

  const handleGoBack = () => {
    navigation.goBack();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Verified':
        return '#00d4aa';
      case 'Pending Consensus':
        return '#ffa500';
      default:
        return '#666';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Verified':
        return '✅';
      case 'Pending Consensus':
        return '⏳';
      default:
        return '❓';
    }
  };

  if (isLoading && !tallyData) {
    return (
      <Container>
        <Header>
          <BackButton onPress={handleGoBack}>
            <BackButtonText>← Back</BackButtonText>
          </BackButton>
          <Title>Live Dashboard</Title>
        </Header>
        <LoadingContainer>
          <LoadingText>Loading election data...</LoadingText>
        </LoadingContainer>
      </Container>
    );
  }

  const totalVotes = tallyData?.nationalTally 
    ? tallyData.nationalTally.candidateA + tallyData.nationalTally.candidateB + tallyData.nationalTally.spoilt
    : 0;

  return (
    <Container>
      <Header>
        <BackButton onPress={handleGoBack}>
          <BackButtonText>← Back</BackButtonText>
        </BackButton>
        <Title>Live Dashboard</Title>
      </Header>

      <ScrollContainer
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }>
        
        {/* National Tally Section */}
        <Section>
          <SectionTitle>National Tally</SectionTitle>
          {tallyData?.nationalTally && (
            <TallyCard>
              <CandidateRow>
                <CandidateLabel>Candidate A</CandidateLabel>
                <CandidateVotes>{tallyData.nationalTally.candidateA.toLocaleString()}</CandidateVotes>
              </CandidateRow>
              
              <CandidateRow>
                <CandidateLabel>Candidate B</CandidateLabel>
                <CandidateVotes>{tallyData.nationalTally.candidateB.toLocaleString()}</CandidateVotes>
              </CandidateRow>
              
              <CandidateRow>
                <CandidateLabel>Spoilt Ballots</CandidateLabel>
                <CandidateVotes>{tallyData.nationalTally.spoilt.toLocaleString()}</CandidateVotes>
              </CandidateRow>
              
              <Divider />
              
              <TotalRow>
                <TotalLabel>Total Votes</TotalLabel>
                <TotalVotes>{totalVotes.toLocaleString()}</TotalVotes>
              </TotalRow>
            </TallyCard>
          )}
        </Section>

        {/* Statistics Section */}
        <Section>
          <SectionTitle>Statistics</SectionTitle>
          <StatsContainer>
            <StatCard>
              <StatValue>{tallyData?.verifiedStations || 0}</StatValue>
              <StatLabel>Verified Stations</StatLabel>
            </StatCard>
            
            <StatCard>
              <StatValue>{tallyData?.totalStations || 0}</StatValue>
              <StatLabel>Total Stations</StatLabel>
            </StatCard>
            
            <StatCard>
              <StatValue>
                {tallyData?.totalStations 
                  ? Math.round((tallyData.verifiedStations / tallyData.totalStations) * 100)
                  : 0}%
              </StatValue>
              <StatLabel>Completion</StatLabel>
            </StatCard>
          </StatsContainer>
        </Section>

        {/* Polling Stations Section */}
        <Section>
          <SectionTitle>Polling Stations</SectionTitle>
          {tallyData?.pollingStations.map((station) => (
            <StationCard key={station.id}>
              <StationHeader>
                <StationId>{station.id}</StationId>
                <StatusContainer>
                  <StatusIcon>{getStatusIcon(station.status)}</StatusIcon>
                  <StatusText color={getStatusColor(station.status)}>
                    {station.status}
                  </StatusText>
                </StatusContainer>
              </StationHeader>
              
              {station.results && (
                <StationResults>
                  <ResultRow>
                    <ResultLabel>A: {station.results.candidateA}</ResultLabel>
                    <ResultLabel>B: {station.results.candidateB}</ResultLabel>
                    <ResultLabel>Spoilt: {station.results.spoilt}</ResultLabel>
                  </ResultRow>
                </StationResults>
              )}
              
              <StationFooter>
                <SubmissionCount>
                  {station.submissionCount} submission{station.submissionCount !== 1 ? 's' : ''}
                </SubmissionCount>
              </StationFooter>
            </StationCard>
          ))}
        </Section>

        {/* Last Updated */}
        <UpdateInfo>
          <UpdateText>Last updated: {lastUpdated}</UpdateText>
          <UpdateText>Auto-refresh: 30s</UpdateText>
        </UpdateInfo>
      </ScrollContainer>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: #1a1a2e;
`;

const Header = styled.View`
  flex-direction: row;
  align-items: center;
  padding: 20px;
  padding-top: 40px;
`;

const BackButton = styled.TouchableOpacity`
  margin-right: 16px;
`;

const BackButtonText = styled.Text`
  color: #00d4aa;
  font-size: 16px;
`;

const Title = styled.Text`
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

const LoadingText = styled.Text`
  color: #ffffff;
  font-size: 16px;
`;

const ScrollContainer = styled.ScrollView`
  flex: 1;
  padding: 0 20px;
`;

const Section = styled.View`
  margin-bottom: 24px;
`;

const SectionTitle = styled.Text`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
`;

const TallyCard = styled.View`
  background-color: #16213e;
  border-radius: 12px;
  padding: 20px;
`;

const CandidateRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const CandidateLabel = styled.Text`
  color: #ffffff;
  font-size: 16px;
`;

const CandidateVotes = styled.Text`
  color: #00d4aa;
  font-size: 18px;
  font-weight: 600;
`;

const Divider = styled.View`
  height: 1px;
  background-color: #333;
  margin: 12px 0;
`;

const TotalRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

const TotalLabel = styled.Text`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
`;

const TotalVotes = styled.Text`
  color: #00d4aa;
  font-size: 20px;
  font-weight: bold;
`;

const StatsContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
`;

const StatCard = styled.View`
  background-color: #16213e;
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  flex: 1;
  margin: 0 4px;
`;

const StatValue = styled.Text`
  color: #00d4aa;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
`;

const StatLabel = styled.Text`
  color: #ffffff;
  font-size: 12px;
  text-align: center;
`;

const StationCard = styled.View`
  background-color: #16213e;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
`;

const StationHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const StationId = styled.Text`
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
`;

const StatusContainer = styled.View`
  flex-direction: row;
  align-items: center;
`;

const StatusIcon = styled.Text`
  margin-right: 4px;
`;

const StatusText = styled.Text<{color: string}>`
  color: ${props => props.color};
  font-size: 14px;
  font-weight: 500;
`;

const StationResults = styled.View`
  margin-bottom: 8px;
`;

const ResultRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
`;

const ResultLabel = styled.Text`
  color: #ffffff;
  font-size: 14px;
`;

const StationFooter = styled.View`
  align-items: flex-end;
`;

const SubmissionCount = styled.Text`
  color: #666;
  font-size: 12px;
`;

const UpdateInfo = styled.View`
  align-items: center;
  padding: 20px;
`;

const UpdateText = styled.Text`
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
`;

export default DashboardScreen;
