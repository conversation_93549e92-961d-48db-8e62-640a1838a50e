/**
 * Image Capture Screen Component
 * Screen for capturing Form 34A images using device camera
 */

import React, {useState, useRef} from 'react';
import {Alert, Platform} from 'react-native';
import styled from 'styled-components/native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/types';
import {processImageOCR} from '@/services/mlService';
import Button from '@/components/Button';

type ImageCaptureScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'ImageCapture'
>;

const ImageCaptureScreen: React.FC = () => {
  const navigation = useNavigation<ImageCaptureScreenNavigationProp>();
  const [isProcessing, setIsProcessing] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);

  const handleTakePhoto = async () => {
    try {
      // For MVP: Mock camera functionality
      // In production, this would use react-native-camera or react-native-image-picker
      console.log('Taking photo...');
      
      // Simulate camera capture
      const mockImageUri = `file://mock-image-${Date.now()}.jpg`;
      setCapturedImage(mockImageUri);
      
      Alert.alert(
        'Photo Captured',
        'Form 34A image captured successfully. Process for OCR?',
        [
          {text: 'Retake', onPress: () => setCapturedImage(null)},
          {text: 'Process', onPress: () => handleProcessImage(mockImageUri)},
        ],
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to capture image. Please try again.');
    }
  };

  const handleProcessImage = async (imageUri: string) => {
    setIsProcessing(true);
    
    try {
      console.log('Processing image with OCR...');
      const results = await processImageOCR(imageUri);
      
      // Navigate to confirmation screen with extracted results
      navigation.navigate('Confirmation', {
        results,
        submissionType: 'image_ocr',
      });
    } catch (error) {
      Alert.alert(
        'Processing Error',
        'Failed to extract data from image. Please try again.',
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <Container>
      <Header>
        <BackButton onPress={handleGoBack}>
          <BackButtonText>← Back</BackButtonText>
        </BackButton>
        <Title>Capture Form 34A</Title>
      </Header>

      <CameraContainer>
        {capturedImage ? (
          <PreviewContainer>
            <PreviewText>Image Captured</PreviewText>
            <PreviewSubtext>
              Form 34A image ready for OCR processing
            </PreviewSubtext>
          </PreviewContainer>
        ) : (
          <CameraPlaceholder>
            <CameraIcon>📷</CameraIcon>
            <CameraText>Position Form 34A in the frame</CameraText>
            <CameraSubtext>
              Ensure the form is well-lit and all text is clearly visible
            </CameraSubtext>
          </CameraPlaceholder>
        )}
      </CameraContainer>

      <ControlsContainer>
        {capturedImage ? (
          <>
            <Button
              title="Retake Photo"
              onPress={() => setCapturedImage(null)}
              variant="outline"
            />
            <Button
              title={isProcessing ? 'Processing...' : 'Process with OCR'}
              onPress={() => handleProcessImage(capturedImage)}
              loading={isProcessing}
              disabled={isProcessing}
            />
          </>
        ) : (
          <Button
            title="Capture Form 34A"
            onPress={handleTakePhoto}
          />
        )}
      </ControlsContainer>

      <InstructionsContainer>
        <InstructionsTitle>Instructions:</InstructionsTitle>
        <InstructionText>• Ensure good lighting</InstructionText>
        <InstructionText>• Keep the form flat and straight</InstructionText>
        <InstructionText>• Make sure all numbers are clearly visible</InstructionText>
        <InstructionText>• Avoid shadows and reflections</InstructionText>
      </InstructionsContainer>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: #1a1a2e;
`;

const Header = styled.View`
  flex-direction: row;
  align-items: center;
  padding: 20px;
  padding-top: 40px;
`;

const BackButton = styled.TouchableOpacity`
  margin-right: 16px;
`;

const BackButtonText = styled.Text`
  color: #00d4aa;
  font-size: 16px;
`;

const Title = styled.Text`
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
`;

const CameraContainer = styled.View`
  flex: 1;
  margin: 20px;
  border-radius: 12px;
  overflow: hidden;
  background-color: #16213e;
  border: 2px dashed #00d4aa;
`;

const CameraPlaceholder = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 40px;
`;

const CameraIcon = styled.Text`
  font-size: 64px;
  margin-bottom: 20px;
`;

const CameraText = styled.Text`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 8px;
`;

const CameraSubtext = styled.Text`
  color: #ffffff;
  font-size: 14px;
  opacity: 0.7;
  text-align: center;
  line-height: 20px;
`;

const PreviewContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 40px;
`;

const PreviewText = styled.Text`
  color: #00d4aa;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
`;

const PreviewSubtext = styled.Text`
  color: #ffffff;
  font-size: 16px;
  opacity: 0.8;
  text-align: center;
`;

const ControlsContainer = styled.View`
  padding: 20px;
  gap: 12px;
`;

const InstructionsContainer = styled.View`
  padding: 20px;
  background-color: #16213e;
`;

const InstructionsTitle = styled.Text`
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
`;

const InstructionText = styled.Text`
  color: #ffffff;
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 4px;
`;

export default ImageCaptureScreen;
