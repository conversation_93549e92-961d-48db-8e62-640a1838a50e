/**
 * Main Action Screen Component
 * Screen with options to capture form image or record audio announcement
 */

import React from 'react';
import styled from 'styled-components/native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/types';
import {useWalletStore} from '@/state/walletStore';

type MainActionScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'MainAction'
>;

const MainActionScreen: React.FC = () => {
  const navigation = useNavigation<MainActionScreenNavigationProp>();
  const {address} = useWalletStore();

  const handleImageCapture = () => {
    navigation.navigate('ImageCapture');
  };

  const handleAudioCapture = () => {
    navigation.navigate('AudioCapture');
  };

  const handleViewDashboard = () => {
    navigation.navigate('Dashboard');
  };

  return (
    <Container>
      <Header>
        <WelcomeText>Welcome, Witness</WelcomeText>
        <AddressText numberOfLines={1} ellipsizeMode="middle">
          {address}
        </AddressText>
      </Header>

      <ActionsContainer>
        <ActionTitle>Choose Your Witnessing Method</ActionTitle>
        
        <ActionButton onPress={handleImageCapture}>
          <ActionIcon>📷</ActionIcon>
          <ActionText>Capture Form Image</ActionText>
          <ActionSubtext>
            Take a photo of Form 34A for OCR processing
          </ActionSubtext>
        </ActionButton>

        <ActionButton onPress={handleAudioCapture}>
          <ActionIcon>🎤</ActionIcon>
          <ActionText>Record Official Announcement</ActionText>
          <ActionSubtext>
            Record the official results announcement
          </ActionSubtext>
        </ActionButton>
      </ActionsContainer>

      <FooterContainer>
        <DashboardButton onPress={handleViewDashboard}>
          <DashboardButtonText>View Live Tally Dashboard</DashboardButtonText>
        </DashboardButton>
      </FooterContainer>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: #1a1a2e;
  padding: 20px;
`;

const Header = styled.View`
  margin-top: 40px;
  margin-bottom: 40px;
  align-items: center;
`;

const WelcomeText = styled.Text`
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8px;
`;

const AddressText = styled.Text`
  font-size: 12px;
  color: #00d4aa;
  font-family: monospace;
  max-width: 280px;
`;

const ActionsContainer = styled.View`
  flex: 1;
  justify-content: center;
`;

const ActionTitle = styled.Text`
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  margin-bottom: 40px;
`;

const ActionButton = styled.TouchableOpacity`
  background-color: #16213e;
  border: 2px solid #00d4aa;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  align-items: center;
`;

const ActionIcon = styled.Text`
  font-size: 48px;
  margin-bottom: 12px;
`;

const ActionText = styled.Text`
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
  text-align: center;
`;

const ActionSubtext = styled.Text`
  font-size: 14px;
  color: #ffffff;
  opacity: 0.7;
  text-align: center;
  line-height: 20px;
`;

const FooterContainer = styled.View`
  margin-top: 20px;
`;

const DashboardButton = styled.TouchableOpacity`
  background-color: transparent;
  border: 1px solid #00d4aa;
  border-radius: 8px;
  padding: 12px;
  align-items: center;
`;

const DashboardButtonText = styled.Text`
  color: #00d4aa;
  font-size: 16px;
  font-weight: 500;
`;

export default MainActionScreen;
