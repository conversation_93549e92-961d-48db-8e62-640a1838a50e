/**
 * Splash Screen Component
 * Initial screen with wallet connection functionality
 */

import React, {useEffect} from 'react';
import {Alert} from 'react-native';
import styled from 'styled-components/native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/types';
import {useWalletStore} from '@/state/walletStore';

type SplashScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Splash'
>;

const SplashScreen: React.FC = () => {
  const navigation = useNavigation<SplashScreenNavigationProp>();
  const {
    isConnected,
    isConnecting,
    error,
    connectWallet,
    clearError,
  } = useWalletStore();

  useEffect(() => {
    if (isConnected) {
      navigation.replace('MainAction');
    }
  }, [isConnected, navigation]);

  useEffect(() => {
    if (error) {
      Alert.alert('Connection Error', error, [
        {text: 'OK', onPress: clearError},
      ]);
    }
  }, [error, clearError]);

  const handleConnectWallet = async () => {
    try {
      await connectWallet();
    } catch (err) {
      // Error is handled by the store and useEffect above
    }
  };

  return (
    <Container>
      <LogoContainer>
        <Logo>OYAH!</Logo>
        <Subtitle>Decentralized Election Witness</Subtitle>
      </LogoContainer>

      <ButtonContainer>
        <ConnectButton
          onPress={handleConnectWallet}
          disabled={isConnecting}>
          <ButtonText>
            {isConnecting ? 'Connecting...' : 'Connect Wallet to Witness'}
          </ButtonText>
        </ConnectButton>
      </ButtonContainer>

      <FooterText>
        Secure • Transparent • Decentralized
      </FooterText>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: #1a1a2e;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

const LogoContainer = styled.View`
  align-items: center;
  margin-bottom: 80px;
`;

const Logo = styled.Text`
  font-size: 48px;
  font-weight: bold;
  color: #00d4aa;
  margin-bottom: 10px;
`;

const Subtitle = styled.Text`
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  opacity: 0.8;
`;

const ButtonContainer = styled.View`
  width: 100%;
  margin-bottom: 40px;
`;

const ConnectButton = styled.TouchableOpacity<{disabled: boolean}>`
  background-color: ${props => (props.disabled ? '#555' : '#00d4aa')};
  padding: 18px 24px;
  border-radius: 12px;
  align-items: center;
  opacity: ${props => (props.disabled ? 0.6 : 1)};
`;

const ButtonText = styled.Text`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
`;

const FooterText = styled.Text`
  color: #ffffff;
  font-size: 14px;
  opacity: 0.6;
  text-align: center;
`;

export default SplashScreen;
