/**
 * API Service for OYAH! dApp
 * Handles all backend communication and HTTP requests
 */

import axios, {AxiosInstance, AxiosResponse} from 'axios';
import {ResultPayload, TallyResponse} from '@/types';

// Backend API base URL - for MVP, using localhost
// In production, this would be the deployed backend URL
const API_BASE_URL = 'http://localhost:3000/api/v1';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for logging
    this.api.interceptors.request.use(
      config => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      error => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      },
    );

    // Response interceptor for logging and error handling
    this.api.interceptors.response.use(
      response => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      error => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      },
    );
  }

  /**
   * Submit election results to the backend
   */
  async submitResult(payload: ResultPayload): Promise<void> {
    try {
      const response: AxiosResponse = await this.api.post('/submitResult', payload);
      
      if (response.status !== 200 && response.status !== 201) {
        throw new Error(`Failed to submit result: ${response.status}`);
      }

      console.log('Result submitted successfully');
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.message || error.message;
        throw new Error(`Failed to submit result: ${message}`);
      }
      throw new Error('Failed to submit result: Unknown error');
    }
  }

  /**
   * Get current election tally from the backend
   */
  async getTally(): Promise<TallyResponse> {
    try {
      const response: AxiosResponse<TallyResponse> = await this.api.get('/getTally');
      
      if (response.status !== 200) {
        throw new Error(`Failed to get tally: ${response.status}`);
      }

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.message || error.message;
        throw new Error(`Failed to get tally: ${message}`);
      }
      throw new Error('Failed to get tally: Unknown error');
    }
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response: AxiosResponse = await this.api.get('/health');
      return response.status === 200;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Update API base URL (useful for switching between environments)
   */
  updateBaseURL(newBaseURL: string): void {
    this.api.defaults.baseURL = newBaseURL;
    console.log(`API base URL updated to: ${newBaseURL}`);
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;

// Export individual methods for easier importing
export const {submitResult, getTally, healthCheck} = apiService;
