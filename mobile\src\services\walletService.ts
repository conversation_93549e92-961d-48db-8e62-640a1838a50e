/**
 * Wallet service for Polkadot.js API integration
 * Handles Nova Wallet connection and wallet operations
 */

import {web3Accounts, web3Enable, web3FromAddress} from '@polkadot/extension-dapp';
import {ApiPromise, WsProvider} from '@polkadot/api';

let api: ApiPromise | null = null;

/**
 * Initialize Polkadot API connection
 */
export const initializeApi = async (): Promise<ApiPromise> => {
  if (api) {
    return api;
  }

  try {
    // Connect to Polkadot node (using public endpoint for <PERSON>)
    const wsProvider = new WsProvider('wss://rpc.polkadot.io');
    api = await ApiPromise.create({provider: wsProvider});
    return api;
  } catch (error) {
    throw new Error('Failed to initialize Polkadot API');
  }
};

/**
 * Connect to Nova Wallet and get user's address
 */
export const connectWallet = async (): Promise<string> => {
  try {
    // Enable the extension
    const extensions = await web3Enable('OYAH! dApp');
    
    if (extensions.length === 0) {
      throw new Error('No wallet extension found. Please install Nova Wallet.');
    }

    // Get all accounts
    const accounts = await web3Accounts();
    
    if (accounts.length === 0) {
      throw new Error('No accounts found. Please create an account in Nova Wallet.');
    }

    // For MVP, use the first account
    const account = accounts[0];
    return account.address;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to connect to wallet');
  }
};

/**
 * Get wallet address if already connected
 */
export const getWalletAddress = async (): Promise<string | null> => {
  try {
    const accounts = await web3Accounts();
    return accounts.length > 0 ? accounts[0].address : null;
  } catch (error) {
    return null;
  }
};

/**
 * Sign a message with the connected wallet
 */
export const signMessage = async (
  address: string,
  message: string,
): Promise<string> => {
  try {
    const injector = await web3FromAddress(address);
    
    if (!injector.signer.signRaw) {
      throw new Error('Wallet does not support message signing');
    }

    const {signature} = await injector.signer.signRaw({
      address,
      data: message,
      type: 'bytes',
    });

    return signature;
  } catch (error) {
    throw new Error('Failed to sign message');
  }
};

/**
 * Disconnect from the API
 */
export const disconnectApi = async (): Promise<void> => {
  if (api) {
    await api.disconnect();
    api = null;
  }
};
