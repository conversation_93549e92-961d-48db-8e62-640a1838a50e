/**
 * Zustand store for dashboard state management
 */

import {create} from 'zustand';
import {TallyResponse} from '@/types';
import {getTally} from '@/services/apiService';

interface DashboardState {
  tallyData: TallyResponse | null;
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  lastUpdated: string | null;
  autoRefreshEnabled: boolean;
}

interface DashboardActions {
  fetchTallyData: (showLoading?: boolean) => Promise<void>;
  setRefreshing: (isRefreshing: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  toggleAutoRefresh: () => void;
  reset: () => void;
}

type DashboardStore = DashboardState & DashboardActions;

export const useDashboardStore = create<DashboardStore>((set, get) => ({
  // State
  tallyData: null,
  isLoading: false,
  isRefreshing: false,
  error: null,
  lastUpdated: null,
  autoRefreshEnabled: true,

  // Actions
  fetchTallyData: async (showLoading = true) => {
    const {isLoading, isRefreshing} = get();
    
    // Prevent multiple simultaneous requests
    if (isLoading || isRefreshing) return;

    try {
      if (showLoading) {
        set({isLoading: true, error: null});
      } else {
        set({isRefreshing: true, error: null});
      }

      const data = await getTally();
      
      set({
        tallyData: data,
        lastUpdated: new Date().toISOString(),
        error: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch data';
      set({error: errorMessage});
    } finally {
      set({isLoading: false, isRefreshing: false});
    }
  },

  setRefreshing: (isRefreshing: boolean) => {
    set({isRefreshing});
  },

  setError: (error: string | null) => {
    set({error});
  },

  clearError: () => {
    set({error: null});
  },

  toggleAutoRefresh: () => {
    set(state => ({autoRefreshEnabled: !state.autoRefreshEnabled}));
  },

  reset: () => {
    set({
      tallyData: null,
      isLoading: false,
      isRefreshing: false,
      error: null,
      lastUpdated: null,
      autoRefreshEnabled: true,
    });
  },
}));
