/**
 * Zustand store for wallet state management
 */

import {create} from 'zustand';
import {WalletStore} from '@/types';
import {connectWallet as connectWalletService} from '@/services/walletService';

export const useWalletStore = create<WalletStore>((set, get) => ({
  // State
  isConnected: false,
  address: null,
  isConnecting: false,
  error: null,

  // Actions
  connectWallet: async () => {
    const {isConnecting} = get();
    if (isConnecting) return;

    set({isConnecting: true, error: null});

    try {
      const address = await connectWalletService();
      set({
        isConnected: true,
        address,
        isConnecting: false,
        error: null,
      });
    } catch (error) {
      set({
        isConnected: false,
        address: null,
        isConnecting: false,
        error: error instanceof Error ? error.message : 'Failed to connect wallet',
      });
    }
  },

  disconnectWallet: () => {
    set({
      isConnected: false,
      address: null,
      isConnecting: false,
      error: null,
    });
  },

  setError: (error: string | null) => {
    set({error});
  },

  clearError: () => {
    set({error: null});
  },
}));
