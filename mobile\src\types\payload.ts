/**
 * Data payload type definitions for OYAH! dApp
 */

export interface GPSCoordinates {
  latitude: number;
  longitude: number;
}

export interface ElectionResults {
  candidateA: number;
  candidateB: number;
  spoilt: number;
}

export type SubmissionType = 'image_ocr' | 'audio_stt';

export interface ResultPayload {
  walletAddress: string;
  pollingStationId: string;
  gpsCoordinates: GPSCoordinates;
  timestamp: string;
  results: ElectionResults;
  submissionType: SubmissionType;
}

export interface PollingStationStatus {
  id: string;
  status: 'Pending Consensus' | 'Verified';
  results: ElectionResults | null;
}

export interface TallyResponse {
  nationalTally: ElectionResults;
  pollingStations: PollingStationStatus[];
}
