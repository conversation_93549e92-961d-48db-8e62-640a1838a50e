/**
 * Wallet-related type definitions for OYAH! dApp
 */

export interface WalletState {
  isConnected: boolean;
  address: string | null;
  isConnecting: boolean;
  error: string | null;
}

export interface WalletActions {
  connectWallet: () => Promise<void>;
  disconnectWallet: () => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export type WalletStore = WalletState & WalletActions;
