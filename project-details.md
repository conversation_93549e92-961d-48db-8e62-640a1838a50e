# OYAH! MVP Project Plan

## 1. Vision & Scope
To develop a functional MVP of the OYAH! decentralized election witness dApp. The MVP will validate the core user flow of wallet-based authentication, on-device data processing, crowdsourced consensus, and real-time tallying. The primary goal is to create a working proof-of-concept for demonstration and initial user testing.

## 2. Key Milestones & Timeline
*   **Total Estimated Time:** 4 Sprints (8 Weeks)
*   **Milestone 1 (End of Sprint 1):** Wallet Integration Complete. User can connect Nova Wallet to the dApp.
*   **Milestone 2 (End of Sprint 2):** On-Device Data Capture Complete. User can capture image/audio and get confirmed results on-device.
*   **Milestone 3 (End of Sprint 3):** Backend and API Complete. Backend can receive data, apply consensus logic, and serve results.
*   **Milestone 4 (End of Sprint 4):** Full End-to-End Integration. A fully testable dApp ready for demonstration.

## 3. Sprint Breakdown

### Sprint 1: Core dApp & Web3 Foundation (Weeks 1-2)
*   **[Task]** Setup React Native project with TypeScript.
*   **[Task]** Integrate Polkadot.js API libraries.
*   **[Task]** Implement Splash Screen and Nova Wallet connection flow.
*   **[Task]** Implement Zustand for state management (storing wallet address).
*   **[Task]** Build basic navigation structure (Splash -> Main Action -> Dashboard).
*   **[Task]** Design UI mockups for all screens.

### Sprint 2: Data Capture & On-Device ML (Weeks 3-4)
*   **[Task]** Build the native camera module for image capture.
*   **[Task]** Research and integrate a lightweight TFLite OCR model.
*   **[Task]** Build the audio recording module.
*   **[Task]** Research and integrate a lightweight TFLite Speech-to-Text model.
*   **[Task]** Develop the "Confirmation Screen" UI component.
*   **[Task]** Write the service logic for on-device processing and payload assembly.

### Sprint 3: Backend Consensus Engine & API (Weeks 5-6)
*   **[Task]** Setup Node.js + Express.js project with TypeScript.
*   **[Task]** Develop the `/api/v1/submitResult` endpoint.
*   **[Task]** Implement the data clustering logic (by polling station).
*   **[Task]** Implement the MVP consensus algorithm (max of 3+ identical results).
*   **[Task]** Develop the `/api/v1/getTally` endpoint.
*   **[Task]** Setup a simple in-memory data store for the MVP.

### Sprint 4: End-to-End Integration & Polish (Weeks 7-8)
*   **[Task]** Connect the React Native dApp to the backend API endpoints.
*   **[Task]** Implement the Live Tally Dashboard UI with real-time data fetching.
*   **[Task]** Conduct rigorous end-to-end testing of the full user flow.
*   **[Task]** Perform UI/UX polish and bug fixing.
*   **[Task]** Prepare documentation and a demonstration script.
*   **[Task]** Deploy the backend to a cloud service (e.g., Heroku, Vercel).

## 4. Roles & Responsibilities
*   **Project Lead:** Oversees development, ensures alignment with vision.
*   **Frontend/dApp Agent:** Responsible for the React Native application.
*   **Backend Agent:** Responsible for the Node.js consensus engine and API.
*   **QA/Testing:** Responsible for E2E testing and bug reporting.

## 5. Technology Stack Summary
*   **Mobile:** React Native, TypeScript, Polkadot.js, Zustand, Styled Components, TensorFlow Lite
*   **Backend:** Node.js, Express.js, TypeScript