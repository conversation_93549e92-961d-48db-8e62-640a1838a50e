# OYAH! MVP Project Plan

**Project Overview:**
OYAH! is a decentralized mobile application (dApp) designed to bring radical transparency and trust to the electoral process. It empowers ordinary citizens and party agents to act as "witnesses" by securely submitting polling station results. The system's core innovation lies in its use of Web3 technology for identity, crowdsourced consensus for real-time verification, and on-device processing for speed and resilience in low-connectivity environments.

**Core Objective for this MVP:**
Your primary goal is to build a functional end-to-end Minimum Viable Product (MVP) that demonstrates the core user flow: **Connect -> Capture -> Confirm -> Transmit -> Tally.** The MVP must be intuitive, fast, and secure, proving the viability of the core concept. NFT minting and advanced exception handling are out of scope for the MVP.

**Technology Stack:**
*   **Mobile dApp:** React Native with TypeScript
*   **Web3 Integration:** Polkadot.js API (`@polkadot/api`)
*   **On-Device ML:** TensorFlow Lite (for both OCR and Speech-to-Text)
*   **Backend:** Node.js with Express.js (using TypeScript)
*   **State Management (Mobile):** Zustand (for its simplicity and power)
*   **Styling:** Styled Components

**Detailed Feature Breakdown & Implementation Guide:**

**COMPONENT 1: Mobile dApp (OYAH!)**

*   **Feature 1.1: Wallet Authentication**
    *   **Screen:** Splash Screen.
    *   **UI:** A single, prominent button: "Connect Wallet to Witness".
    *   **Logic:**
        1.  On tap, initiate a connection to the user's Nova Wallet using the Polkadot.js API.
        2.  Request read-only access to the user's public wallet address.
        3.  Once connected, securely store the user's address in the app's state (using Zustand) and navigate to the Main Action Screen. The wallet address will serve as the user's unique identifier for all subsequent actions.

*   **Feature 1.2: Main Action Screen**
    *   **UI:** A clean screen with two large, distinct buttons:
        *   A camera icon labeled "Capture Form Image"
        *   A microphone icon labeled "Record Official Announcement"
    *   **Logic:** These buttons navigate to their respective capture modules.

*   **Feature 1.3: Image Capture & On-Device OCR Module**
    *   **Flow:**
        1.  Open a native camera view optimized for document scanning.
        2.  After the user captures the image of Form 34A, do not transmit it.
        3.  Invoke the local TensorFlow Lite OCR model to process the image *on the device*.
        4.  Extract key numerical values (votes per candidate, total votes).
        5.  Display a "Confirmation Screen" showing the extracted numbers in a clear, editable format.
        6.  The user must visually verify and tap "Confirm & Submit".

*   **Feature 1.4: Audio Recording & On-Device Transcription Module**
    *   **Flow:**
        1.  Open a simple audio recording interface.
        2.  After the user records the announcement, do not transmit the audio file.
        3.  Invoke the local TensorFlow Lite Speech-to-Text model to transcribe the audio *on the device*.
        4.  Parse the resulting text to identify and extract key numerical values.
        5.  Display the same "Confirmation Screen" as in 1.3, pre-filled with the extracted numbers.
        6.  The user must visually verify and tap "Confirm & Submit".

*   **Feature 1.5: Data Payload Assembly & Transmission**
    *   **Logic:** Upon user confirmation from either path (1.3 or 1.4), assemble a small JSON object.
    *   **JSON Payload Structure:**
        ```json
        {
          "walletAddress": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
          "pollingStationId": "KAS-01-123", // Note: For MVP, this can be manually entered or selected from a list.
          "gpsCoordinates": { "latitude": -1.286389, "longitude": 36.817223 },
          "timestamp": "2025-08-09T14:30:00Z",
          "results": {
            "candidateA": 152,
            "candidateB": 210,
            "spoilt": 12
          },
          "submissionType": "image_ocr" // or "audio_stt"
        }
        ```
    *   Transmit this JSON object via a POST request to the backend API endpoint.

*   **Feature 1.6: Live Tally Dashboard**
    *   **UI:** A simple, clean mobile view.
    *   **Features:**
        *   Display a national-level aggregate tally for each candidate.
        *   Include a list of polling stations. Each station should have a status indicator:
            *   **Yellow (Pending Consensus):** Results have been received but not yet verified.
            *   **Green (Verified by Crowd):** Consensus has been reached.
    *   **Logic:** The dashboard will periodically fetch the latest tally data from the backend's results API endpoint and update the UI.

**COMPONENT 2: Backend Consensus Engine (Node.js)**

*   **Feature 2.1: Ingestion API Endpoint**
    *   Create a single POST endpoint (e.g., `/api/v1/submitResult`) that accepts the JSON payload defined in 1.5.
    *   Validate the incoming data structure.

*   **Feature 2.2: Data Clustering & MVP Consensus Algorithm**
    *   **Logic:**
        1.  On receiving a payload, group it with others from the same `pollingStationId`.
        2.  For that polling station, compare the `results` object from all submissions.
        3.  **Consensus Rule:** If a minimum of **3 unique `walletAddress`es** submit the exact same `results` object, and this group constitutes the majority of submissions for that station, that result is considered **"Verified."**
        4.  Store the status (Pending/Verified) and the verified result in a temporary in-memory database or a simple database like Redis or a JSON file for the MVP.

*   **Feature 2.3: Results API Endpoint**
    *   Create a GET endpoint (e.g., `/api/v1/getTally`) that the mobile dApp can call.
    *   **Response Structure:**
        ```json
        {
          "nationalTally": { "candidateA": 125430, "candidateB": 145210, "spoilt": 8950 },
          "pollingStations": [
            { "id": "KAS-01-123", "status": "Verified", "results": { ... } },
            { "id": "KAS-01-124", "status": "Pending Consensus", "results": null }
          ]
        }
        ```
